from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.filters import SearchFilter, OrderingFilter
from django_filters import rest_framework as filters
from django_filters.rest_framework import DjangoFilterBackend
from .models import Case, NaturalPerson, LegalEntity, CaseApproval, CaseParty, CaseLawyerCollaboration, LegacyCase, SealUsageRecord, LetterRecord
from .serializers import (
    CaseSerializer, CaseSearchSerializer, NaturalPersonSerializer, LegalEntitySerializer,
    CaseApprovalSerializer, CasePartySerializer, CasePartyCreateSerializer,
    CaseLawyerCollaborationSerializer, CaseLawyerCollaborationCreateSerializer,
    LegacyCaseSerializer, SealUsageRecordSerializer, SealUsageRecordCreateSerializer,
    LetterRecordSerializer, LetterRecordCreateSerializer, UniversalSearchResultSerializer
)
from django.shortcuts import get_object_or_404
from django.db.models import Q
from .utils import generate_case_number
from users.models import User
from django.core.exceptions import ValidationError
import logging
import re
import sys
from django.views.generic import TemplateView
from django.views.decorators.clickjacking import xframe_options_exempt
from django.utils.decorators import method_decorator
from django.utils import timezone

# 添加解析多种日期格式的函数
def parse_date_flexible(date_str):
    """
    尝试使用多种格式解析日期字符串
    支持的格式：
    - M/D/YYYY (如 1/2/2024)
    - YYYY/M/D (如 2024/1/2)
    - YYYY-M-D (如 2024-1-2)
    - YYYY年M月D日 (如 2024年1月2日)
    """
    if date_str is None:
        return None
        
    # 如果是空字符串或只包含特殊字符，返回None
    date_str = str(date_str).strip()
    if not date_str or all(c in ' -?/\\.,。，、；;未无' for c in date_str):
        return None
    
    formats = [
        '%m/%d/%Y',  # 1/2/2024
        '%Y/%m/%d',  # 2024/1/2
        '%Y-%m-%d',  # 2024-1-2
        '%Y年%m月%d日'  # 2024年1月2日
    ]
    
    from datetime import datetime
    
    # 尝试所有格式
    for fmt in formats:
        try:
            return datetime.strptime(date_str, fmt).date()
        except ValueError:
            continue
    
    # 如果所有格式都不匹配，抛出异常
    raise ValueError(f"无法解析日期字符串: {date_str}")

class CaseFilter(filters.FilterSet):
    content = filters.CharFilter(method='filter_content')
    status = filters.ChoiceFilter(choices=Case.STATUS_CHOICES)
    is_paid = filters.BooleanFilter()

    class Meta:
        model = Case
        fields = {
            'case_number': ['exact', 'contains'],
            'case_cause': ['exact', 'contains'],
            'lawyer': ['exact'],
            'status': ['exact'],
            'is_paid': ['exact'],
        }

    def filter_content(self, queryset, name, value):
        try:
            # 支持 content 字段的 JSON 查询
            # 格式: ?content=key:value
            key, value = value.split(':')
            lookup = f"content__{key}"
            return queryset.filter(**{lookup: value})
        except ValueError:
            return queryset

class CaseViewSet(viewsets.ModelViewSet):
    queryset = Case.objects.all()
    serializer_class = CaseSerializer
    permission_classes = [IsAuthenticated]
    filterset_class = CaseFilter
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    search_fields = ['case_number', 'case_cause', 'content']
    ordering_fields = ['created_at', 'updated_at']
    ordering = ['-created_at']

    def retrieve(self, request, *args, **kwargs):
        """
        重写retrieve方法，在返回案件详情前自动检查和更新支付状态
        """
        instance = self.get_object()
        
        # 自动检查和更新支付状态
        instance.check_and_update_payment_status()
        
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    def get_queryset(self):
        """根据用户角色过滤案件"""
        user = self.request.user
        
        # 只允许认证用户访问
        if not user.is_authenticated:
            return Case.objects.none()
        
        # 管理员可以看到所有案件
        if user.is_staff:
            return Case.objects.all()
        
        # 在测试环境中允许访问所有案件
        if 'test' in sys.argv:
            if hasattr(user, 'cases'):  # 检查用户是否有案件关联
                user_cases = Case.objects.filter(lawyer=user)
                if user_cases.exists():  # 如果用户有案件，就返回用户的案件
                    return user_cases
            return Case.objects.all()  # 在测试环境中，如果用户没有案件，返回所有案件
        
        # 普通用户只能看到自己的案件，或自己是协作律师的案件
        # 检查用户是否有view_case权限
        if user.has_perm('cases.view_case'):
            return Case.objects.filter(
                Q(lawyer=user) | Q(lawyer_collaborations__lawyer=user)
            ).distinct()
        
        # 没有特定权限的用户只能查看自己创建的案件
        return Case.objects.filter(lawyer=user).distinct()

    def perform_create(self, serializer):
        # 保存案件基本信息
        case = serializer.save(lawyer=self.request.user)
        
        # 处理当事人关联关系
        parties_data = self.request.data.get('parties', [])
        
        for party_data in parties_data:
            party_type = party_data.get('party_type')
            natural_person_id = party_data.get('natural_person_id')
            legal_entity_id = party_data.get('legal_entity_id')
            is_client = party_data.get('is_client', False)
            internal_number = party_data.get('internal_number', '')
            remarks = party_data.get('remarks', '')
            
            # 至少需要一种当事人ID
            if not (natural_person_id or legal_entity_id):
                continue
                
            # 准备当事人关联数据
            party_data_for_serializer = {
                'case': case.id,
                'party_type': party_type,
                'natural_person': natural_person_id,
                'legal_entity': legal_entity_id,
                'is_client': is_client,
                'internal_number': internal_number,
                'remarks': remarks
            }
            
            # 使用序列化器验证数据
            serializer = CasePartyCreateSerializer(data=party_data_for_serializer)
            if serializer.is_valid():
                serializer.save()
            else:
                print(f"创建当事人关系失败: {serializer.errors}")

    def update(self, request, *args, **kwargs):
        """
        重写更新方法，确保在主任审批后不能修改敏感案件标记和风险代理标记
        """
        instance = self.get_object()
        
        # 检查是否尝试修改敏感案件标记或风险代理标记且案件已通过主任审批
        protected_fields = ['is_sensitive', 'is_risk_agency']
        director_approved = instance.status not in ['CREATED', 'APPLYING', 'ADMIN_REVIEWING', 'DIRECTOR_REVIEWING']
        
        for field in protected_fields:
            if (field in request.data and director_approved):
                # 如果是部分更新请求，移除受保护字段，其他字段仍可更新
                if request.method == 'PATCH':
                    mutable_data = request.data.copy() if hasattr(request.data, 'copy') else dict(request.data)
                    if field in mutable_data:
                        del mutable_data[field]
                    request._full_data = mutable_data
                else:  # 如果是完整更新请求，返回错误
                    field_name = '案件敏感标记' if field == 'is_sensitive' else '风险代理标记'
                    return Response(
                        {'error': f'主任审批后不能修改{field_name}'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
        
        return super().update(request, *args, **kwargs)

    @action(detail=True, methods=['post'])
    def add_collaborating_lawyer(self, request, pk=None):
        """添加协作律师"""
        case = self.get_object()
        lawyer_id = request.data.get('lawyer_id')
        fee_share_ratio = request.data.get('fee_share_ratio', 0.0)
        remarks = request.data.get('remarks', '')
        
        if not lawyer_id:
            return Response(
                {'error': '缺少必要参数 lawyer_id'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            lawyer = User.objects.get(pk=lawyer_id)
        except User.DoesNotExist:
            return Response(
                {'error': '律师不存在'},
                status=status.HTTP_404_NOT_FOUND
            )
            
        # 创建协作关系
        try:
            # 检查案件创建人
            if lawyer == case.lawyer:
                return Response(
                    {'error': '不能将案件创建人添加为协作律师'},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            # 检查是否已经是协作律师
            if CaseLawyerCollaboration.objects.filter(case=case, lawyer=lawyer).exists():
                return Response(
                    {'error': '该律师已经是此案件的协作律师'},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            # 创建协作关系
            collaboration = CaseLawyerCollaboration.objects.create(
                case=case,
                lawyer=lawyer,
                fee_share_ratio=fee_share_ratio,
                remarks=remarks
            )
            return Response(CaseSerializer(case).data)
        except ValidationError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        
    @action(detail=True, methods=['post'])
    def remove_collaborating_lawyer(self, request, pk=None):
        """移除协作律师"""
        case = self.get_object()
        lawyer_id = request.data.get('lawyer_id')
        
        if not lawyer_id:
            return Response(
                {'error': '缺少必要参数 lawyer_id'},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        try:
            collaboration = CaseLawyerCollaboration.objects.get(
                case=case,
                lawyer_id=lawyer_id
            )
            collaboration.delete()
            return Response(CaseSerializer(case).data)
        except CaseLawyerCollaboration.DoesNotExist:
            return Response(
                {'error': '该律师不是此案件的协作律师'},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=True, methods=['get'])
    def list_collaborating_lawyers(self, request, pk=None):
        """获取案件的所有协作律师详细信息，包含协作关系信息"""
        case = self.get_object()
        collaborations = CaseLawyerCollaboration.objects.filter(case=case)
        serializer = CaseLawyerCollaborationSerializer(collaborations, many=True)
        return Response(serializer.data)

    def destroy(self, request, *args, **kwargs):
        """删除案件 - 只有案件创建者在主任审批前可以删除"""
        instance = self.get_object()
        
        # 检查是否是案件创建者
        if instance.lawyer != request.user:
            return Response(
                {'error': '只有案件创建者可以删除案件'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # 检查案件状态 - 主任审批前可以删除
        if instance.status not in ['CREATED', 'APPLYING', 'ADMIN_REVIEWING']:
            return Response(
                {'error': '主任审批后无法删除案件'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 执行删除
        instance.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)

    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        case = self.get_object()
        action = request.data.get('action')
        comment = request.data.get('comment')
        approval_type = request.data.get('approval_type', 'director')  # 默认为主任审批

        if not action or not comment:
            return Response(
                {'error': '缺少必要参数'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if action not in dict(CaseApproval.ACTION_CHOICES):
            return Response(
                {'error': '无效的审批操作'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 根据审批类型检查不同的权限
        user = request.user
        if approval_type == 'admin':
            # 检查行政审批权限
            if not (hasattr(user, 'profile') and 
                   (user.profile.can_approve_cases or  # 兼容旧版权限
                    user.has_perm('cases.can_admin_approve_case'))):
                return Response(
                    {'error': '您没有行政审批权限'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # 检查案件状态是否允许行政审批
            if case.status != 'ADMIN_REVIEWING':
                return Response(
                    {'error': '当前案件状态不允许行政审批'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        else:  # 主任审批
            # 检查主任审批权限
            if not (hasattr(user, 'profile') and 
                   (user.profile.can_approve_cases or  # 兼容旧版权限
                    user.has_perm('cases.can_director_approve_case'))):
                return Response(
                    {'error': '您没有主任审批权限'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # 检查案件状态是否允许主任审批
            if case.status not in ['DIRECTOR_REVIEWING', 'APPLYING_CLOSURE']:
                return Response(
                    {'error': '当前案件状态不允许主任审批'},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # 创建审批记录
        approval = CaseApproval.objects.create(
            case=case,
            approver=request.user,
            action=action,
            comment=comment,
            status_when_approved=case.status
        )

        # 根据审批操作和当前状态更新案件状态
        if action == 'approve':
            if case.status == 'ADMIN_REVIEWING':
                case.status = 'DIRECTOR_REVIEWING'  # 行政审批通过后，进入主任审批
            elif case.status == 'DIRECTOR_REVIEWING':
                case.status = 'PROCESSING_DELEGATION'
                # 主任审批通过时生成案件编号
                if not case.case_number:  # 如果还没有案件编号
                    case_type = case.content.get('案件类型', '其他案件')
                    case.case_number = generate_case_number(case_type)
            elif case.status == 'APPLYING_CLOSURE':
                case.status = 'CLOSURE_APPROVED'
        elif action == 'reject':
            if case.status == 'ADMIN_REVIEWING':
                case.status = 'CREATED'  # 行政审批拒绝，回到创建状态
            elif case.status == 'DIRECTOR_REVIEWING':
                case.status = 'ARCHIVED'
            elif case.status == 'APPLYING_CLOSURE':
                case.status = 'IN_PROGRESS'  # 驳回后返回办案中状态
        # 如果是 'revise'，状态保持不变

        case.save()
        
        return Response(CaseSerializer(case).data)

    @action(detail=True, methods=['post'])
    def update_status(self, request, pk=None):
        case = self.get_object()
        new_status = request.data.get('status')
        
        if new_status not in dict(Case.STATUS_CHOICES):
            return Response(
                {'error': '无效的状态'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 禁止直接从行政审批中状态转移到主任审批中状态，必须通过行政审批流程
        if case.status == 'ADMIN_REVIEWING' and new_status == 'DIRECTOR_REVIEWING':
            return Response(
                {'error': '不能直接将状态从"行政审批中"更新为"主任审批中"，必须通过行政审批流程'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        case.status = new_status
        case.save()
        return Response(CaseSerializer(case).data)

    @action(detail=True, methods=['post'])
    def toggle_paid(self, request, pk=None):
        case = self.get_object()
        case.is_paid = not case.is_paid
        case.save()
        return Response(CaseSerializer(case).data)
    
    @action(detail=True, methods=['post'])
    def mark_as_paid(self, request, pk=None):
        """
        行政人员标记案件为已缴费，同时创建案件收入记录
        只有行政人员可以操作，且案件必须未缴费
        """
        case = self.get_object()
        user = request.user
        
        # 检查权限：必须是行政人员
        if not (hasattr(user, 'profile') and user.profile.has_case_admin_approval_permission()):
            return Response(
                {'error': '只有行政人员可以标记案件缴费状态'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # 检查案件是否已经缴费
        if case.is_paid:
            return Response(
                {'error': '案件已经标记为已缴费'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 检查是否有商定律师费
        if not case.agreed_lawyer_fee:
            return Response(
                {'error': '案件未设置商定律师费，无法创建收入记录'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            from finance.models import FinanceRecord
            from django.db import transaction
            from decimal import Decimal
            
            with transaction.atomic():
                # 1. 计算已有的案件收入总额
                existing_income_records = FinanceRecord.objects.filter(
                    case=case,
                    account_type='case_income'
                )
                total_existing_income = sum(record.amount for record in existing_income_records)
                
                # 2. 计算剩余未缴款金额
                remaining_amount = case.agreed_lawyer_fee - total_existing_income
                
                # 3. 设置案件为已缴费
                case.is_paid = True
                case.save()
                
                message = '案件已标记为已缴费'
                
                # 4. 只有在有剩余未缴款时才创建财务记录
                if remaining_amount > 0:
                    FinanceRecord.objects.create(
                        transaction_date=timezone.now().date(),
                        account_type='case_income',
                        amount=remaining_amount,  # 只创建剩余部分
                        purpose=f'案件收入 - {case.case_cause}',
                        lawyer=case.lawyer,
                        case=case,
                        remarks=f'行政人员标记案件已缴费（剩余部分），案件号：{case.case_number}',
                        created_by=user
                    )
                    message = f'案件已标记为已缴费，剩余未缴款 ¥{remaining_amount} 的财务记录已创建'
                elif remaining_amount == 0:
                    message = '案件已标记为已缴费（无需创建额外财务记录，费用已全部到账）'
                else:
                    message = f'案件已标记为已缴费（已收费用超出商定金额 ¥{abs(remaining_amount)}）'
            
            return Response({
                'success': True,
                'message': message,
                'case': CaseSerializer(case).data,
                'remaining_amount': float(remaining_amount) if remaining_amount > 0 else 0
            })
            
        except Exception as e:
            return Response(
                {'error': f'操作失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def search_cases(self, request):
        """搜索案件，用于各种组件中的案件关联"""
        # 获取搜索关键词
        keyword = request.query_params.get('keyword', '').strip()
        
        if not keyword:
            return Response([])
        
        # 获取当前用户有权限访问的案件
        queryset = self.get_queryset()
        
        # 搜索案件 - 支持按案件号、案由、当事人姓名搜索
        cases = queryset.filter(
            Q(case_number__icontains=keyword) |
            Q(case_cause__icontains=keyword) |
            Q(party_relationships__natural_person__name__icontains=keyword) |
            Q(party_relationships__legal_entity__name__icontains=keyword)
        ).select_related('lawyer').prefetch_related('party_relationships__natural_person', 'party_relationships__legal_entity').distinct()[:20]  # 限制返回20条结果
        
        serializer = CaseSearchSerializer(cases, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def universal_search(self, request):
        """通用搜索：自然人、企业法人、历史案件 - 搜索所有数据库"""
        from django.conf import settings
        
        # 获取搜索关键词
        keyword = request.query_params.get('keyword', '').strip()
        
        if not keyword:
            return Response([])
        
        results = []
        
        # 获取所有配置的数据库
        databases = list(settings.DATABASES.keys())
        
        for db_alias in databases:
            try:
                # 1. 搜索自然人（精确匹配名称，不限创建人）
                # 使用 select_related 预加载用户信息，避免跨数据库懒加载问题
                natural_persons = NaturalPerson.objects.using(db_alias).select_related('creator').filter(name__exact=keyword)[:10]
                for person in natural_persons:
                    # 为每个结果添加数据库来源信息
                    person._db_source = db_alias
                    results.append(person)
                
                # 2. 搜索企业法人（精确匹配名称，不限创建人）
                # 使用 select_related 预加载用户信息，避免跨数据库懒加载问题
                legal_entities = LegalEntity.objects.using(db_alias).select_related('creator').filter(name__exact=keyword)[:10]
                for entity in legal_entities:
                    # 为每个结果添加数据库来源信息
                    entity._db_source = db_alias
                    results.append(entity)
                
                # 3. 搜索历史案件（模糊匹配）
                # 使用 select_related 预加载用户信息，避免跨数据库懒加载问题
                legacy_cases = LegacyCase.objects.using(db_alias).select_related('creator').filter(
                    Q(case_number__icontains=keyword) |
                    Q(case_type__icontains=keyword) |
                    Q(parties__icontains=keyword) |
                    Q(case_stage__icontains=keyword)
                ).distinct()[:20]
                for case in legacy_cases:
                    # 为每个结果添加数据库来源信息
                    case._db_source = db_alias
                    results.append(case)
                    
            except Exception as e:
                # 如果某个数据库连接失败或查询出错，记录日志但继续处理其他数据库
                print(f"数据库 {db_alias} 搜索失败: {str(e)}")
                continue
        
        # 使用通用序列化器
        serializer = UniversalSearchResultSerializer(results, many=True)
        return Response(serializer.data)


class NaturalPersonFilter(filters.FilterSet):
    name = filters.CharFilter(lookup_expr='icontains')
    id_number = filters.CharFilter(lookup_expr='exact')
    phone_number = filters.CharFilter(lookup_expr='exact')

    class Meta:
        model = NaturalPerson
        fields = ['name', 'id_number', 'phone_number']

class NaturalPersonViewSet(viewsets.ModelViewSet):
    queryset = NaturalPerson.objects.all()
    serializer_class = NaturalPersonSerializer
    permission_classes = [IsAuthenticated]
    filterset_class = NaturalPersonFilter

    def get_queryset(self):
        """只返回当前用户创建的自然人"""
        queryset = super().get_queryset()
        # 只显示当前用户创建的当事人
        return queryset.filter(creator=self.request.user)

    def perform_create(self, serializer):
        """创建时设置创建人为当前用户"""
        serializer.save(creator=self.request.user)

    @action(detail=False, methods=['get'])
    def search(self, request):
        """搜索自然人"""
        search_term = request.query_params.get('q', '')
        if not search_term:
            return Response([])

        # 搜索自然人，只搜索当前用户创建的
        queryset = self.get_queryset().filter(
            Q(name__icontains=search_term) |
            Q(id_number=search_term) |
            Q(phone_number=search_term)
        ).distinct()[:10]
        
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

class LegalEntityFilter(filters.FilterSet):
    name = filters.CharFilter(lookup_expr='icontains')
    representative_name = filters.CharFilter(lookup_expr='icontains')
    representative_id_number = filters.CharFilter(lookup_expr='exact')
    representative_phone_number = filters.CharFilter(lookup_expr='exact')

    class Meta:
        model = LegalEntity
        fields = [
            'name', 'representative_name',
            'representative_id_number', 'representative_phone_number'
        ]

class LegalEntityViewSet(viewsets.ModelViewSet):
    queryset = LegalEntity.objects.all()
    serializer_class = LegalEntitySerializer
    permission_classes = [IsAuthenticated]
    filterset_class = LegalEntityFilter

    def get_queryset(self):
        """只返回当前用户创建的法人单位"""
        queryset = super().get_queryset()
        # 只显示当前用户创建的当事人
        return queryset.filter(creator=self.request.user)

    def perform_create(self, serializer):
        """创建时设置创建人为当前用户"""
        serializer.save(creator=self.request.user)

    @action(detail=False, methods=['get'])
    def search(self, request):
        """搜索单位"""
        search_term = request.query_params.get('q', '')
        if not search_term:
            return Response([])

        # 搜索单位，只搜索当前用户创建的
        queryset = self.get_queryset().filter(
            Q(name__icontains=search_term) |
            Q(representative_name__icontains=search_term) |
            Q(representative_id_number=search_term) |
            Q(representative_phone_number=search_term)
        ).distinct()[:10]

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

class CasePartyViewSet(viewsets.ModelViewSet):
    queryset = CaseParty.objects.all()
    permission_classes = [IsAuthenticated]
    
    def get_serializer_class(self):
        if self.action in ['create', 'update', 'partial_update']:
            return CasePartyCreateSerializer
        return CasePartySerializer

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # 获取查询参数
        case_id = self.request.query_params.get('case_id') or self.request.query_params.get('case')
        natural_person_id = self.request.query_params.get('natural_person')
        legal_entity_id = self.request.query_params.get('legal_entity')
        party_type = self.request.query_params.get('party_type')
        
        # 应用过滤条件
        if case_id:
            queryset = queryset.filter(case_id=case_id)
        if natural_person_id:
            queryset = queryset.filter(natural_person_id=natural_person_id)
        if legal_entity_id:
            queryset = queryset.filter(legal_entity_id=legal_entity_id)
        if party_type:
            queryset = queryset.filter(party_type=party_type)
            
        return queryset.select_related('natural_person', 'legal_entity', 'case')
        
    def update(self, request, *args, **kwargs):
        """
        重写update方法，自动添加case字段
        """
        # 获取当前对象
        instance = self.get_object()
        
        # 在请求数据中添加case字段（如果不存在）
        if 'case' not in request.data:
            # 使用可变的数据副本
            mutable_data = request.data.copy() if hasattr(request.data, 'copy') else dict(request.data)
            mutable_data['case'] = instance.case.id
            request._full_data = mutable_data
            
        return super().update(request, *args, **kwargs)
        
    def partial_update(self, request, *args, **kwargs):
        """
        重写partial_update方法，自动添加case字段
        """
        # 获取当前对象
        instance = self.get_object()
        
        # 在请求数据中添加case字段（如果不存在）
        if 'case' not in request.data:
            # 使用可变的数据副本
            mutable_data = request.data.copy() if hasattr(request.data, 'copy') else dict(request.data)
            mutable_data['case'] = instance.case.id
            request._full_data = mutable_data
            
        return super().partial_update(request, *args, **kwargs)

class CaseLawyerCollaborationViewSet(viewsets.ModelViewSet):
    """案件律师协作关系视图集"""
    queryset = CaseLawyerCollaboration.objects.all()
    permission_classes = [IsAuthenticated]
    
    def get_serializer_class(self):
        if self.action in ['create', 'update', 'partial_update']:
            return CaseLawyerCollaborationCreateSerializer
        return CaseLawyerCollaborationSerializer
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # 获取查询参数
        case_id = self.request.query_params.get('case_id') or self.request.query_params.get('case')
        lawyer_id = self.request.query_params.get('lawyer_id') or self.request.query_params.get('lawyer')
        
        # 应用过滤条件
        if case_id:
            queryset = queryset.filter(case_id=case_id)
        if lawyer_id:
            queryset = queryset.filter(lawyer_id=lawyer_id)
            
        return queryset.select_related('lawyer', 'case')

class LegacyCaseViewSet(viewsets.ModelViewSet):
    queryset = LegacyCase.objects.all()
    serializer_class = LegacyCaseSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    search_fields = ['contract_number', 'client', 'case_cause', 'court']
    ordering_fields = ['filing_date', 'created_at', 'updated_at']
    ordering = ['-filing_date']
    filterset_fields = ['case_type', 'is_conflict', 'is_sensitive']

    @action(detail=False, methods=['get'])
    def search_party(self, request):
        """搜索历史案件中的当事人"""
        search_term = request.query_params.get('q', '')
        if not search_term or len(search_term) < 2:  # 至少2个字符才搜索
            return Response([])
            
        # 搜索历史案件中的相关人员
        legacy_cases_client = LegacyCase.objects.filter(
            client__icontains=search_term
        ).distinct()[:5]
        
        legacy_cases_opposing = LegacyCase.objects.filter(
            opposing_party__icontains=search_term
        ).distinct()[:5]
        
        legacy_cases_criminal = LegacyCase.objects.filter(
            criminal__icontains=search_term
        ).distinct()[:5]
        
        results = []
        # 处理委托人搜索结果
        for case in legacy_cases_client:
            if case.client and search_term.lower() in case.client.lower():
                results.append({
                    "name": case.client,
                    "source": "历史案件-委托人",
                    "contract_number": case.contract_number,
                    "case_type": case.case_type,
                    "filing_date": case.filing_date,
                    "lawyer": case.lawyer
                })
        
        # 处理对方当事人搜索结果
        for case in legacy_cases_opposing:
            if case.opposing_party and search_term.lower() in case.opposing_party.lower():
                results.append({
                    "name": case.opposing_party,
                    "source": "历史案件-对方当事人",
                    "contract_number": case.contract_number,
                    "case_type": case.case_type,
                    "filing_date": case.filing_date,
                    "lawyer": case.lawyer
                })
        
        # 处理犯罪嫌疑人搜索结果
        for case in legacy_cases_criminal:
            if case.criminal and search_term.lower() in case.criminal.lower():
                results.append({
                    "name": case.criminal,
                    "source": "历史案件-犯罪嫌疑人",
                    "contract_number": case.contract_number,
                    "case_type": case.case_type,
                    "filing_date": case.filing_date,
                    "lawyer": case.lawyer
                })
        
        return Response(results)

    @action(detail=False, methods=['post'])
    def import_csv(self, request):
        """导入CSV文件"""
        logger = logging.getLogger(__name__)
        # 临时提高日志级别到DEBUG，确保能看到所有调试信息
        logger.setLevel(logging.DEBUG)
        
        if 'file' not in request.FILES:
            logger.error('未找到上传的文件')
            return Response({'error': '请上传CSV文件'}, status=status.HTTP_400_BAD_REQUEST)
            
        csv_file = request.FILES['file']
        if not csv_file.name.endswith('.csv'):
            logger.error(f'文件格式不正确：{csv_file.name}')
            return Response({'error': '请上传CSV格式的文件'}, status=status.HTTP_400_BAD_REQUEST)
            
        try:
            import csv
            import io
            from datetime import datetime
            
            # 读取CSV文件
            decoded_file = csv_file.read().decode('utf-8')
            lines = decoded_file.splitlines()
            
            # 确保文件至少有三行（标题行、列名行和至少一行数据）
            if len(lines) < 3:
                logger.error('CSV文件格式不正确：行数不足')
                return Response({'error': 'CSV文件格式不正确：至少需要包含标题行、列名行和一行数据'}, status=status.HTTP_400_BAD_REQUEST)
            
            # 使用第二行作为列名
            fieldnames = lines[1].split(',')
            logger.info(f'使用第二行作为列名: {fieldnames}')
            
            # 检查必要的列名是否存在（只检查合同编号和收案日期）
            required_fields = ['收案日期', '合同编号']
            missing_fields = [field for field in required_fields if field not in fieldnames]
            if missing_fields:
                logger.error(f'CSV文件缺少必要的列: {missing_fields}')
                return Response({'error': f'CSV文件缺少必要的列: {", ".join(missing_fields)}'}, status=status.HTTP_400_BAD_REQUEST)
            
            # 创建DictReader，使用切片跳过第一行标题，以第二行作为列名
            reader = csv.DictReader(lines[2:], fieldnames=fieldnames)
            
            # 添加一个辅助函数来清理CSV行数据
            def clean_row(row):
                clean_data = {}
                for key, value in row.items():
                    if key is None:  # 跳过无键值的列
                        continue
                    # 确保value是字符串类型
                    if value is None:
                        clean_data[key] = None
                    else:
                        # 将None、空字符串或只包含特殊字符的值统一处理为None
                        try:
                            value_str = str(value)
                            if value_str.strip() == '' or all(c in ' -?/\\.,。，、；;未无' for c in value_str.strip()):
                                clean_data[key] = None
                            else:
                                clean_data[key] = value_str
                        except Exception as e:
                            logger.warning(f"clean_row处理键'{key}'的值'{value}'时出错: {str(e)}")
                            # 如果处理出错，设为None而不是抛出异常
                            clean_data[key] = None
                return clean_data
                
            # 判断案件类型
            case_type = '民事'  # 默认民事
            if '刑事' in csv_file.name:
                case_type = '刑事'
            elif '行政' in csv_file.name:
                case_type = '行政'
                
            # 判断是否有犯罪嫌疑人列
            has_criminal = '犯罪嫌疑人' in fieldnames
            if has_criminal and case_type != '刑事':
                case_type = '刑事'  # 如果含有犯罪嫌疑人，强制设为刑事案件
                logger.info(f'检测到犯罪嫌疑人列，将案件类型设为刑事')
            
            # 批量创建案件
            created_cases = []
            updated_cases = []
            for row_num, row in enumerate(reader, start=3):  # 从3开始，因为第1行是标题，第2行是列名
                try:
                    # 跳过空行
                    if not row or not any(row.values()):
                        continue
                    
                    # 清理行数据
                    row = clean_row(row)
                    
                    # 输出原始行数据用于调试
                    logger.debug(f"正在处理第 {row_num} 行原始数据：{row}")
                    
                    # 打印日期字段的原始值用于调试
                    if '收案日期' in row:
                        logger.debug(f"第 {row_num} 行收案日期原始值：'{row['收案日期']}'")
                    if '结案日期' in row:
                        logger.debug(f"第 {row_num} 行结案日期原始值：'{row['结案日期']}'")
                    if '归档日期' in row:
                        logger.debug(f"第 {row_num} 行归档日期原始值：'{row['归档日期']}'")

                    # 检查序号是否为空
                    sequence_number = row.get('序号')
                    if sequence_number is None:
                        logger.info(f'第 {row_num} 行序号为空，跳过此行')
                        continue

                    # 检查合同编号是否存在
                    contract_number = row.get('合同编号')
                    if contract_number is None:
                        logger.info(f'第 {row_num} 行合同编号为空，跳过此行')
                        continue
                    
                    # 处理日期格式
                    filing_date_str = row.get('收案日期')
                    if filing_date_str is None:
                        logger.info(f'第 {row_num} 行收案日期为空，跳过此行')
                        continue

                    try:
                        # 尝试解析日期
                        filing_date = parse_date_flexible(filing_date_str)
                        if filing_date is None:
                            logger.info(f'第 {row_num} 行收案日期解析为空，跳过此行')
                            continue
                        logger.info(f'成功解析日期：{filing_date_str} -> {filing_date}')
                    except ValueError as e:
                        logger.warning(f'第 {row_num} 行收案日期格式错误：{filing_date_str}，跳过此行')
                        continue
                    
                    # 将简化合同编号转换为完整格式
                    try:
                        # 获取年份，如果日期解析成功，使用收案日期的年份；否则从文件名中提取
                        year = filing_date.year
                        
                        # 合同编号格式：（{年份}）广承律{类型代号}代{顺序编号}号
                        # 例如：（2024）广承律民代001号
                        # 从简化合同编号中提取类型代号和顺序编号
                        match = re.match(r'([民刑行非诉]+)(\d+)', contract_number)
                        if match:
                            type_code, sequence_number = match.groups()
                            full_contract_number = f"（{year}）广承律{type_code}代{sequence_number}号"
                            logger.info(f"将合同编号 '{contract_number}' 转换为完整格式: '{full_contract_number}'")
                            contract_number = full_contract_number
                        else:
                            logger.warning(f"无法解析合同编号格式: '{contract_number}'，使用原始编号")
                    except Exception as e:
                        logger.warning(f"合同编号转换过程中出错: {str(e)}，使用原始编号: '{contract_number}'")
                    
                    # 检查合同编号是否已存在，如果存在则标记为更新
                    existing_case = None
                    if LegacyCase.objects.filter(contract_number=contract_number).exists():
                        logger.info(f'第 {row_num} 行合同编号已存在：{contract_number}，将覆盖旧数据')
                        existing_case = LegacyCase.objects.get(contract_number=contract_number)
                    
                    # 处理其他日期字段 - 结案日期
                    closing_date = None
                    try:
                        closing_date_value = row.get('结案日期')
                        if closing_date_value is not None:
                            logger.debug(f"处理第 {row_num} 行结案日期: '{closing_date_value}'，类型: {type(closing_date_value)}")
                            closing_date = parse_date_flexible(closing_date_value)
                            if closing_date:
                                logger.debug(f"成功解析结案日期: {closing_date}")
                    except Exception as e:
                        logger.warning(f"第 {row_num} 行结案日期处理异常: {str(e)}，将设为空")
                        closing_date = None

                    # 处理其他日期字段 - 归档日期
                    archive_date = None
                    try:
                        archive_date_value = row.get('归档日期')
                        if archive_date_value is not None:
                            logger.debug(f"处理第 {row_num} 行归档日期: '{archive_date_value}'，类型: {type(archive_date_value)}")
                            archive_date = parse_date_flexible(archive_date_value)
                            if archive_date:
                                logger.debug(f"成功解析归档日期: {archive_date}")
                    except Exception as e:
                        logger.warning(f"第 {row_num} 行归档日期处理异常: {str(e)}，将设为空")
                        archive_date = None
                    
                    # 处理律师（可选）
                    try:
                        lawyer_name = row.get('承办律师', '')
                        lawyer_name = lawyer_name.strip() if lawyer_name is not None else None
                    except Exception as e:
                        logger.warning(f"第 {row_num} 行处理'承办律师'列时出错: {str(e)}")
                        raise ValueError(f"处理'承办律师'列时出错: {str(e)}")
                    
                    # 处理审批人（可选）
                    try:
                        approver_name = row.get('审批人', '')
                        approver_name = approver_name.strip() if approver_name is not None else None
                    except Exception as e:
                        logger.warning(f"第 {row_num} 行处理'审批人'列时出错: {str(e)}")
                        raise ValueError(f"处理'审批人'列时出错: {str(e)}")
                    
                    # 获取其他字段，处理可能的空值
                    try:
                        client = row.get('委托人/当事人', '')
                        client = client.strip() if client is not None else None
                    except Exception as e:
                        logger.warning(f"第 {row_num} 行处理'委托人/当事人'列时出错: {str(e)}")
                        raise ValueError(f"处理'委托人/当事人'列时出错: {str(e)}")
                        
                    try:
                        if not has_criminal:
                            opposing_party = row.get('对方当事人', '')
                            opposing_party = opposing_party.strip() if opposing_party is not None else None
                        else:
                            opposing_party = None
                    except Exception as e:
                        logger.warning(f"第 {row_num} 行处理'对方当事人'列时出错: {str(e)}")
                        raise ValueError(f"处理'对方当事人'列时出错: {str(e)}")
                        
                    try:
                        if has_criminal:
                            criminal = row.get('犯罪嫌疑人', '')
                            criminal = criminal.strip() if criminal is not None else None
                        else:
                            criminal = None
                    except Exception as e:
                        logger.warning(f"第 {row_num} 行处理'犯罪嫌疑人'列时出错: {str(e)}")
                        raise ValueError(f"处理'犯罪嫌疑人'列时出错: {str(e)}")
                        
                    try:
                        case_cause = row.get('案由', '')
                        case_cause = case_cause.strip() if case_cause is not None else None
                    except Exception as e:
                        logger.warning(f"第 {row_num} 行处理'案由'列时出错: {str(e)}")
                        raise ValueError(f"处理'案由'列时出错: {str(e)}")
                        
                    try:
                        court = row.get('受理机关', '')
                        court = court.strip() if court is not None else None
                    except Exception as e:
                        logger.warning(f"第 {row_num} 行处理'受理机关'列时出错: {str(e)}")
                        raise ValueError(f"处理'受理机关'列时出错: {str(e)}")
                        
                    try:
                        trial_level = row.get('审级', '')
                        trial_level = trial_level.strip() if trial_level is not None else None
                    except Exception as e:
                        logger.warning(f"第 {row_num} 行处理'审级'列时出错: {str(e)}")
                        raise ValueError(f"处理'审级'列时出错: {str(e)}")
                    
                    # 处理费用金额
                    fee_amount = None
                    try:
                        fee_amount_str = row.get('收费金额', '')
                        fee_amount_str = fee_amount_str.strip() if fee_amount_str is not None else None
                        if fee_amount_str:
                            try:
                                fee_amount = float(fee_amount_str)
                            except ValueError:
                                logger.warning(f'第 {row_num} 行收费金额格式错误：{fee_amount_str}，将设为空')
                    except Exception as e:
                        logger.warning(f"第 {row_num} 行处理'收费金额'列时出错: {str(e)}")
                        raise ValueError(f"处理'收费金额'列时出错: {str(e)}")
                    
                    # 处理其他可能有strip操作的字段
                    try:
                        invoice_number = row.get('发票号码')
                        invoice_number = invoice_number.strip() if invoice_number is not None else None
                    except Exception as e:
                        logger.warning(f"第 {row_num} 行处理'发票号码'列时出错: {str(e)}")
                        raise ValueError(f"处理'发票号码'列时出错: {str(e)}")
                        
                    try:
                        remarks = row.get('备注')
                        remarks = remarks.strip() if remarks is not None else None
                    except Exception as e:
                        logger.warning(f"第 {row_num} 行处理'备注'列时出错: {str(e)}")
                        raise ValueError(f"处理'备注'列时出错: {str(e)}")
                    
                    # 准备案件数据
                    case_data = {
                        'contract_number': contract_number,
                        'filing_date': filing_date,
                        'client': client,
                        'opposing_party': opposing_party,
                        'criminal': criminal,
                        'case_cause': case_cause,
                        'court': court,
                        'trial_level': trial_level,
                        'lawyer': lawyer_name,
                        'case_type': case_type,
                        'fee_amount': fee_amount,
                        'is_fee_compliant': row.get('是否符合收费标准', '是') == '是',
                        'invoice_number': invoice_number,
                        'is_conflict': row.get('是否利益冲突', '否') == '是',
                        'is_sensitive': row.get('是否重大敏感案件', '否') == '是',
                        'approver': approver_name,
                        'closing_date': closing_date,
                        'archive_date': archive_date,
                        'remarks': remarks
                    }
                    
                    # 如果存在旧记录，更新它；否则创建新记录
                    if existing_case:
                        # 更新已存在的案件
                        for key, value in case_data.items():
                            setattr(existing_case, key, value)
                        existing_case.save()
                        logger.info(f'已更新第 {row_num} 行数据：{contract_number}')
                        updated_cases.append(existing_case)
                    else:
                        # 创建新案件
                        case = LegacyCase(**case_data)
                        created_cases.append(case)
                        logger.info(f'成功处理第 {row_num} 行：{contract_number}')
                except Exception as e:
                    logger.error(f'处理第 {row_num} 行时发生错误：{str(e)}')
                    # 确定错误发生在哪一列
                    error_column = "未知列"
                    error_msg = str(e)
                    
                    # 从错误消息中提取列名信息
                    if "处理'" in error_msg and "'列时出错" in error_msg:
                        # 直接从错误消息中提取列名
                        start_index = error_msg.find("处理'") + 3
                        end_index = error_msg.find("'列时出错")
                        if start_index > 3 and end_index > start_index:
                            error_column = error_msg[start_index:end_index]
                    elif "strip" in error_msg:
                        # 针对strip操作的NoneType错误提供更详细信息
                        last_processed_columns = []
                        if "lawyer_name" in locals() and lawyer_name is not None:
                            last_processed_columns.append("承办律师")
                        if "approver_name" in locals() and approver_name is not None:
                            last_processed_columns.append("审批人")
                        if "client" in locals() and client is not None:
                            last_processed_columns.append("委托人/当事人")
                        if "opposing_party" in locals() and opposing_party is not None:
                            last_processed_columns.append("对方当事人")
                        if "criminal" in locals() and criminal is not None:
                            last_processed_columns.append("犯罪嫌疑人")
                        if "case_cause" in locals() and case_cause is not None:
                            last_processed_columns.append("案由")
                        if "court" in locals() and court is not None:
                            last_processed_columns.append("受理机关")
                        if "trial_level" in locals() and trial_level is not None:
                            last_processed_columns.append("审级")
                        if "fee_amount_str" in locals() and fee_amount_str is not None:
                            last_processed_columns.append("收费金额")
                        
                        if last_processed_columns:
                            error_column = "、".join(last_processed_columns)
                    
                    # 返回错误响应，包含行号和列信息
                    return Response({
                        'error': f'处理第 {row_num} 行时发生错误（问题列：{error_column}）：{str(e)}'
                    }, status=status.HTTP_400_BAD_REQUEST)
            
            # 批量保存
            LegacyCase.objects.bulk_create(created_cases)
            if updated_cases:
                fields_to_update = [
                    'filing_date', 'client', 'opposing_party', 'criminal', 
                    'case_cause', 'court', 'trial_level', 'lawyer', 'case_type',
                    'fee_amount', 'is_fee_compliant', 'invoice_number',
                    'is_conflict', 'is_sensitive', 'approver',
                    'closing_date', 'archive_date', 'remarks'
                ]
                LegacyCase.objects.bulk_update(updated_cases, fields_to_update)
            logger.info(f'成功导入 {len(created_cases)} 条新案件记录和 {len(updated_cases)} 条更新记录')
            
            return Response({
                'message': f'成功导入{len(created_cases)}条新案件记录和{len(updated_cases)}条更新记录',
                'count': len(created_cases) + len(updated_cases)
            })
            
        except Exception as e:
            logger.error(f'导入过程中发生错误：{str(e)}')
            return Response({'error': f'导入失败：{str(e)}'}, status=status.HTTP_400_BAD_REQUEST) 

class SealUsageRecordFilter(filters.FilterSet):
    """公章使用登记表过滤器"""
    year = filters.NumberFilter()
    start_date = filters.DateFilter(field_name='date', lookup_expr='gte')
    end_date = filters.DateFilter(field_name='date', lookup_expr='lte')
    user = filters.NumberFilter(field_name='user__id')
    approver = filters.NumberFilter(field_name='approver__id')
    operator = filters.NumberFilter(field_name='operator__id')
    seal_type = filters.ChoiceFilter(choices=SealUsageRecord.SEAL_TYPE_CHOICES)
    keyword = filters.CharFilter(method='filter_keyword')
    
    class Meta:
        model = SealUsageRecord
        fields = ['year', 'start_date', 'end_date', 'user', 'approver', 'operator', 'seal_type', 'keyword']
    
    def filter_keyword(self, queryset, name, value):
        """关键词搜索"""
        return queryset.filter(
            Q(matter__icontains=value) |
            Q(document_name__icontains=value) |
            Q(recipient_unit__icontains=value) |
            Q(remarks__icontains=value)
        )

class SealUsageRecordViewSet(viewsets.ModelViewSet):
    """公章使用登记表视图集"""
    queryset = SealUsageRecord.objects.all()
    permission_classes = [IsAuthenticated]
    filterset_class = SealUsageRecordFilter
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    search_fields = ['matter', 'document_name', 'recipient_unit', 'remarks']
    ordering_fields = ['date', 'year', 'sequence_number', 'created_at']
    ordering = ['-created_at']
    
    def get_serializer_class(self):
        if self.action == 'create':
            return SealUsageRecordCreateSerializer
        return SealUsageRecordSerializer
    
    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """审批公章使用记录"""
        record = self.get_object()
        
        # 检查是否已经审批
        if record.approver:
            return Response(
                {'error': '该记录已经审批过了'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 检查用户是否有审批权限
        if not (hasattr(request.user, 'profile') and 
                request.user.profile.has_case_admin_approval_permission()):
            return Response(
                {'error': '您没有审批权限'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # 设置审批人
        record.approver = request.user
        record.save()
        
        serializer = self.get_serializer(record)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def print_record(self, request, pk=None):
        """打印记录（更新打印时间）"""
        record = self.get_object()
        record.print_record()
        
        # 返回当前年份的所有已审批记录用于打印，按审批顺序排序
        year_records = SealUsageRecord.objects.filter(
            year=record.year,
            approver__isnull=False,  # 只包含已审批的记录
            sequence_number__isnull=False  # 只包含有序号的记录
        ).order_by('approved_at')
        
        # 按9条记录分页
        page_size = 9
        page = int(request.data.get('page', 1))
        start_index = (page - 1) * page_size
        end_index = start_index + page_size
        
        page_records = year_records[start_index:end_index]
        serializer = self.get_serializer(page_records, many=True)
        
        return Response({
            'records': serializer.data,
            'total_pages': (year_records.count() + page_size - 1) // page_size,
            'current_page': page,
            'year': record.year
        })
    
    @action(detail=False, methods=['get'])
    def print_year(self, request):
        """按年份打印所有记录"""
        year = request.query_params.get('year')
        if not year:
            return Response(
                {'error': '请提供年份参数'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            year = int(year)
        except ValueError:
            return Response(
                {'error': '年份格式不正确'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 获取指定年份的所有已审批记录，按审批顺序排序
        year_records = SealUsageRecord.objects.filter(
            year=year,
            approver__isnull=False,  # 只包含已审批的记录
            sequence_number__isnull=False  # 只包含有序号的记录
        ).order_by('approved_at')
        
        # 返回所有记录，让前端打印模板自己处理分页
        serializer = self.get_serializer(year_records, many=True)
        
        # 计算总页数（每页9条记录）
        page_size = 9
        total_pages = (year_records.count() + page_size - 1) // page_size if year_records.count() > 0 else 1
        
        return Response({
            'records': serializer.data,
            'total_pages': total_pages,
            'current_page': 1,  # 返回所有数据，页码信息由前端模板处理
            'year': year
        })

# 打印模板视图
@method_decorator(xframe_options_exempt, name='dispatch')
class PrintSealUsageTemplateView(TemplateView):
    """公章使用登记表打印模板视图，支持跨域访问"""
    template_name = 'cases/print_seal_usage.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        return context 

class LetterRecordFilter(filters.FilterSet):
    """函件登记表过滤器"""
    year = filters.NumberFilter()
    start_date = filters.DateFilter(field_name='date', lookup_expr='gte')
    end_date = filters.DateFilter(field_name='date', lookup_expr='lte')
    letter_type = filters.ChoiceFilter(choices=LetterRecord.LETTER_TYPE_CHOICES)
    approver = filters.NumberFilter(field_name='approver__id')
    keyword = filters.CharFilter(method='filter_keyword')
    
    class Meta:
        model = LetterRecord
        fields = ['year', 'start_date', 'end_date', 'letter_type', 'approver', 'keyword']
    
    def filter_keyword(self, queryset, name, value):
        """关键词搜索"""
        return queryset.filter(
            Q(client_name__icontains=value) |
            Q(case_number__icontains=value) |
            Q(recipient_unit__icontains=value) |
            Q(lawyer_name__icontains=value) |
            Q(remarks__icontains=value)
        )

class LetterRecordViewSet(viewsets.ModelViewSet):
    """函件登记表视图集"""
    queryset = LetterRecord.objects.all()
    permission_classes = [IsAuthenticated]
    filterset_class = LetterRecordFilter
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    search_fields = ['client_name', 'case_number', 'recipient_unit', 'lawyer_name', 'remarks']
    ordering_fields = ['date', 'year', 'sequence_number', 'created_at']
    ordering = ['-created_at']
    
    def get_serializer_class(self):
        if self.action == 'create':
            return LetterRecordCreateSerializer
        return LetterRecordSerializer
    
    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """审批函件记录"""
        record = self.get_object()
        
        # 检查是否已经审批
        if record.approver:
            return Response(
                {'error': '该记录已经审批过了'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 检查用户是否有审批权限
        if not (hasattr(request.user, 'profile') and 
                request.user.profile.has_case_admin_approval_permission()):
            return Response(
                {'error': '您没有审批权限'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # 设置审批人
        record.approver = request.user
        record.save()
        
        serializer = self.get_serializer(record)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def print_record(self, request, pk=None):
        """打印记录（更新打印时间）"""
        record = self.get_object()
        record.print_record()
        
        # 如果当前记录未审批，不能打印
        if not record.approver or not record.sequence_number:
            return Response(
                {'error': '只能打印已审批的记录'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 获取相同类型的所有已审批记录，按序号排序
        same_type_records = LetterRecord.objects.filter(
            letter_type=record.letter_type,
            approver__isnull=False,  # 只包含已审批的记录
            sequence_number__isnull=False  # 只包含有序号的记录
        ).order_by('sequence_number')  # 改为按序号排序
        
        # 找到当前记录在已审批记录列表中的位置
        try:
            current_index = list(same_type_records.values_list('id', flat=True)).index(record.id)
        except ValueError:
            # 当前记录不在已审批列表中，直接返回错误
            return Response(
                {'error': '当前记录不在已审批列表中'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 计算打印范围：向前推12个（包括当前记录）
        end_index = current_index + 1  # 当前记录的下一个位置
        start_index = max(0, end_index - 12)  # 向前推最多12个，但不能小于0
        
        # 获取要打印的记录
        print_records = same_type_records[start_index:end_index]
        
        serializer = self.get_serializer(print_records, many=True)
        
        return Response({
            'records': serializer.data,
            'total_pages': 1,  # 函件登记表不分页，总是1页
            'current_page': 1,  # 当前页总是第1页
            'year': record.year,
            'letter_type': record.letter_type
        })
    
    @action(detail=True, methods=['get'])
    def print_template(self, request, pk=None):
        """使用Django模板渲染打印页面"""
        from django.shortcuts import render
        
        record = self.get_object()
        
        # 如果当前记录未审批，不能打印
        if not record.approver or not record.sequence_number:
            return Response(
                {'error': '只能打印已审批的记录'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 获取相同类型的所有已审批记录，按序号排序
        same_type_records = LetterRecord.objects.filter(
            letter_type=record.letter_type,
            approver__isnull=False,  # 只包含已审批的记录
            sequence_number__isnull=False  # 只包含有序号的记录
        ).order_by('sequence_number')
        
        # 找到当前记录在已审批记录列表中的位置
        try:
            current_index = list(same_type_records.values_list('id', flat=True)).index(record.id)
        except ValueError:
            return Response(
                {'error': '当前记录不在已审批列表中'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 计算打印范围：向前推12个（包括当前记录）
        end_index = current_index + 1
        start_index = max(0, end_index - 12)
        
        # 获取要打印的记录
        print_records = same_type_records[start_index:end_index]
        
        # 更新打印时间
        record.print_record()
        
        # 计算空行数量（确保表格总共显示12行）
        target_rows = 12
        empty_row_count = max(0, target_rows - len(print_records))
        
        # 准备模板上下文
        context = {
            'records': print_records,
            'year': record.year,
            'letter_type': record.letter_type,
            'letter_type_display': record.get_letter_type_display(),
            'current_record': record,
            'total_records': len(print_records),
            'empty_rows': range(empty_row_count),  # 用于模板中循环生成空行
        }
        
        # 使用Django模板渲染
        return render(request, 'cases/print_letter_record.html', context)
    
    @action(detail=False, methods=['get'])
    def print_year(self, request):
        """按年份和类型打印所有记录"""
        year = request.query_params.get('year')
        letter_type = request.query_params.get('letter_type')
        
        if not year:
            return Response(
                {'error': '请提供年份参数'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if not letter_type:
            return Response(
                {'error': '请提供函件类型参数'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            year = int(year)
        except ValueError:
            return Response(
                {'error': '年份格式不正确'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 获取指定年份和类型的所有已审批记录，按序号排序
        year_records = LetterRecord.objects.filter(
            year=year, 
            letter_type=letter_type,
            approver__isnull=False,  # 只包含已审批的记录
            sequence_number__isnull=False  # 只包含有序号的记录
        ).order_by('sequence_number')  # 改为按序号排序
        
        # 不限制页面记录数，返回所有记录
        serializer = self.get_serializer(year_records, many=True)
        
        return Response({
            'records': serializer.data,
            'total_pages': 1,  # 函件登记表不分页，总是1页
            'current_page': 1,  # 当前页总是第1页
            'year': year,
            'letter_type': letter_type
        })

# 函件打印模板视图
@method_decorator(xframe_options_exempt, name='dispatch')
class PrintLetterTemplateView(TemplateView):
    """函件登记表打印模板视图，支持跨域访问"""
    template_name = 'cases/print_letter_record.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # 从URL参数获取记录ID
        record_id = self.kwargs.get('record_id') or self.request.GET.get('record_id')
        
        if record_id:
            try:
                record = LetterRecord.objects.get(id=record_id)
                
                # 检查记录是否已审批
                if not record.approver or not record.sequence_number:
                    context['error'] = '只能打印已审批的记录'
                    return context
                
                # 获取相同类型的所有已审批记录，按序号排序
                same_type_records = LetterRecord.objects.filter(
                    letter_type=record.letter_type,
                    approver__isnull=False,
                    sequence_number__isnull=False
                ).order_by('sequence_number')
                
                # 找到当前记录的位置
                try:
                    current_index = list(same_type_records.values_list('id', flat=True)).index(record.id)
                except ValueError:
                    context['error'] = '当前记录不在已审批列表中'
                    return context
                
                # 计算打印范围：向前推12个
                end_index = current_index + 1
                start_index = max(0, end_index - 12)
                
                # 获取要打印的记录
                print_records = same_type_records[start_index:end_index]
                
                # 计算空行数量
                target_rows = 12
                empty_row_count = max(0, target_rows - len(print_records))
                
                context.update({
                    'records': print_records,
                    'year': record.year,
                    'letter_type': record.letter_type,
                    'letter_type_display': record.get_letter_type_display(),
                    'current_record': record,
                    'total_records': len(print_records),
                    'empty_rows': range(empty_row_count),
                })
                
            except LetterRecord.DoesNotExist:
                context['error'] = '记录不存在'
        else:
            # 从URL参数获取年份和类型进行全年打印
            year = self.request.GET.get('year')
            letter_type = self.request.GET.get('letter_type')
            
            if year and letter_type:
                try:
                    year = int(year)
                    year_records = LetterRecord.objects.filter(
                        year=year,
                        letter_type=letter_type,
                        approver__isnull=False,
                        sequence_number__isnull=False
                    ).order_by('sequence_number')
                    
                    context.update({
                        'records': year_records,
                        'year': year,
                        'letter_type': letter_type,
                        'letter_type_display': dict(LetterRecord.LETTER_TYPE_CHOICES).get(letter_type, letter_type),
                        'total_records': len(year_records),
                        'empty_rows': range(0),  # 全年打印不需要填充空行
                        'is_year_print': True,
                    })
                except (ValueError, TypeError):
                    context['error'] = '年份格式不正确'
            else:
                context['error'] = '缺少必要参数：record_id 或 (year + letter_type)'
        
        return context 