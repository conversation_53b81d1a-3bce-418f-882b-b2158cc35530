services:
  web:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    command: >
      sh -c "bash /wait-for-it.sh db &&
             python manage.py migrate &&
             python manage.py migrate --database firm2 &&
             python manage.py runserver 0.0.0.0:8000"
    volumes:
      - ./backend:/app
      - ./fonts:/app/fonts
    ports:
      - "8000:8000"
    environment:
      - DJANGO_DEBUG=True
      - DJANGO_SETTINGS_MODULE=law_firm.settings
      - POSTGRES_HOST=db
    env_file:
      - ./backend/.env
    depends_on:
      - db

  db:
    image: postgres:15
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/tmp/init-db.sql
    env_file:
      - ./backend/.env
    ports:
      - "5432:5432"
    command: >
      bash -c "
        docker-entrypoint.sh postgres &
        while ! pg_isready -h localhost -p 5432 -U $$POSTGRES_USER; do
          echo 'Waiting for PostgreSQL to start...'
          sleep 2
        done
        echo 'PostgreSQL is ready. Running initialization script...'
        psql -U $$POSTGRES_USER -d $$POSTGRES_DB -f /tmp/init-db.sql || true
        echo 'Initialization script completed.'
        wait
      "

  frontend:
    platform: linux/amd64
    build: 
      context: ./frontend
      dockerfile: Dockerfile
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "4200:4200"
    environment:
      - NODE_ENV=development
      # - NPM_CONFIG_OPTIONAL=false
      - NPM_CONFIG_PLATFORM=linux
      - NPM_CONFIG_ARCH=x64
    deploy:
      resources:
        limits:
          memory: 4G
    depends_on:
      - web

volumes:
  postgres_data: