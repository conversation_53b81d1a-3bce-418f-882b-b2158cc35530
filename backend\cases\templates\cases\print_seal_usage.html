<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公章使用登记表</title>
    <style>
        @media print {
            body {
                margin: 0;
                padding: 20px;
                font-family: "SimSun", serif;
                font-size: 12px;
                line-height: 1.4;
            }
            .no-print {
                display: none !important;
            }
            .page-break {
                page-break-before: always;
            }
        }
        
        @media screen {
            body {
                margin: 0;
                padding: 20px;
                font-family: "SimSun", serif;
                font-size: 12px;
                line-height: 1.4;
                background-color: #f5f5f5;
            }
            .print-container {
                background-color: white;
                padding: 20px;
                margin: 0 auto;
                max-width: 210mm;
                min-height: 297mm;
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
            }
        }
        
        .company-name {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        h1 {
            text-align: center;
            font-size: 16px;
            font-weight: bold;
            margin: 20px 0;
        }
        
        .year-info {
            text-align: center;
            font-size: 14px;
            margin-bottom: 20px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 11px;
        }
        
        th, td {
            border: 1px solid #000;
            padding: 4px 2px;
            text-align: center;
            vertical-align: middle;
            word-wrap: break-word;
        }
        
        th {
            background-color: #f0f0f0;
            font-weight: bold;
            height: 30px;
        }
        
        td {
            height: 25px;
            min-height: 25px;
        }
        
        .col-seq { width: 8%; }
        .col-date { width: 10%; }
        .col-matter { width: 15%; }
        .col-doc-name { width: 12%; }
        .col-quantity { width: 6%; }
        .col-seal-type { width: 8%; }
        .col-recipient { width: 12%; }
        .col-user { width: 8%; }
        .col-approver { width: 8%; }
        .col-operator { width: 8%; }
        .col-remarks { width: 5%; }
        
        .empty-row td {
            height: 25px;
            border: 1px solid #000;
        }
        
        .page-info {
            text-align: center;
            margin-bottom: 10px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="print-container">
        <!-- 页面内容将通过JavaScript动态创建 -->
    </div>
    
    <script>
        // 从URL参数获取数据
        const urlParams = new URLSearchParams(window.location.search);
        const printData = JSON.parse(decodeURIComponent(urlParams.get('data')));
        
        const records = printData.records || [];
        const year = printData.year;
        const totalPages = printData.total_pages || 1;
        
        // 每页显示的记录数
        const recordsPerPage = 9;
        
        // 处理用户显示名称
        function getUserDisplayName(user) {
            if (!user) return '';
            if (user.last_name || user.first_name) {
                return `${user.last_name || ''}${user.first_name || ''}`.trim();
            }
            return user.username || `用户ID: ${user.id}`;
        }
        
        // 创建页面内容
        function createPage(pageRecords, pageNumber, totalPages) {
            const pageDiv = document.createElement('div');
            pageDiv.className = 'print-container';
            if (pageNumber > 1) {
                pageDiv.className += ' page-break';
            }
            
            pageDiv.innerHTML = `
                <div class="company-name">{{ firm_name }}</div>
                <h1>公章使用登记表</h1>
                <div class="year-info">${year}年度公章使用登记表</div>
                <div class="page-info">${totalPages > 1 ? `第 ${pageNumber} 页，共 ${totalPages} 页` : ''}</div>
                
                <table>
                    <thead>
                        <tr>
                            <th class="col-seq">序号</th>
                            <th class="col-date">日期</th>
                            <th class="col-matter">事由</th>
                            <th colspan="3">盖章</th>
                            <th class="col-recipient">受送单位</th>
                            <th class="col-user">使用人</th>
                            <th class="col-approver">审批人</th>
                            <th class="col-operator">盖章经办人</th>
                            <th class="col-remarks">备注</th>
                        </tr>
                        <tr>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th class="col-doc-name">文件名称</th>
                            <th class="col-quantity">数量</th>
                            <th class="col-seal-type">用章种类</th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody class="page-table-body">
                        <!-- 数据将通过JavaScript填充 -->
                    </tbody>
                </table>
            `;
            
            const tableBody = pageDiv.querySelector('.page-table-body');
            
            // 填充记录数据
            pageRecords.forEach(record => {
                const row = document.createElement('tr');
                
                // 格式化日期
                const recordDate = new Date(record.date);
                const formattedDate = `${(recordDate.getMonth() + 1).toString().padStart(2, '0')}-${recordDate.getDate().toString().padStart(2, '0')}`;
                
                row.innerHTML = `
                    <td>（${record.year}）公章使用${record.sequence_number}号</td>
                    <td>${formattedDate}</td>
                    <td style="text-align: left; padding-left: 4px;">${record.matter}</td>
                    <td style="text-align: left; padding-left: 4px;">${record.document_name}</td>
                    <td>${record.quantity}</td>
                    <td>${record.seal_type_display || record.seal_type}</td>
                    <td style="text-align: left; padding-left: 4px;">${record.recipient_unit}</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td style="text-align: left; padding-left: 4px;">${record.remarks || ''}</td>
                `;
                tableBody.appendChild(row);
            });
            
            // 填充空行到9行
            const recordCount = pageRecords.length;
            for (let i = recordCount; i < recordsPerPage; i++) {
                const emptyRow = document.createElement('tr');
                emptyRow.className = 'empty-row';
                emptyRow.innerHTML = `
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                `;
                tableBody.appendChild(emptyRow);
            }
            
            return pageDiv;
        }
        
        // 清空原有内容
        const originalContainer = document.querySelector('.print-container');
        const body = document.body;
        body.removeChild(originalContainer);
        
        // 创建所有页面
        for (let page = 1; page <= totalPages; page++) {
            const startIndex = (page - 1) * recordsPerPage;
            const endIndex = Math.min(startIndex + recordsPerPage, records.length);
            const pageRecords = records.slice(startIndex, endIndex);
            
            const pageDiv = createPage(pageRecords, page, totalPages);
            body.appendChild(pageDiv);
        }
        
        // 添加操作按钮到最后一页
        const lastPage = body.lastElementChild;
        const buttonDiv = document.createElement('div');
        buttonDiv.className = 'no-print';
        buttonDiv.style.cssText = 'margin-top: 20px; text-align: center;';
        buttonDiv.innerHTML = `
            <button onclick="window.print()" style="padding: 8px 16px; margin-right: 10px; background-color: #1976d2; color: white; border: none; border-radius: 4px; cursor: pointer;">打印</button>
            <button onclick="window.close()" style="padding: 8px 16px; background-color: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer;">关闭</button>
        `;
        lastPage.appendChild(buttonDiv);
        
        // 自动打印（可选）
        // window.onload = function() {
        //     window.print();
        // };
    </script>
</body>
</html> 