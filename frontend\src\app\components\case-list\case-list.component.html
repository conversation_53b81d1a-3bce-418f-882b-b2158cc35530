<div class="case-list-container">
  <div class="header-section">
    <h1>案件管理</h1>
    <button mat-raised-button color="primary" [routerLink]="['/cases/create']">
      <mat-icon>add</mat-icon> 创建新案件
    </button>
  </div>

  <div class="header">
    <h2>案件列表</h2>
    <div class="filters-section">
      <!-- 过滤器卡片 -->
      <div class="filter-card">
        <div class="filter-row">
          <!-- 全部案件/我的案件过滤器 -->
          <div class="filter-group">
            <mat-button-toggle-group [(ngModel)]="filter" (change)="onFilterChange()">
              <mat-button-toggle value="all">全部案件 ({{ allCasesCount }})</mat-button-toggle>
              <mat-button-toggle value="mine">我的案件 ({{ myCasesCount }})</mat-button-toggle>
            </mat-button-toggle-group>
          </div>

          <!-- 案件类型过滤器 -->
          <mat-form-field appearance="outline" class="filter-field">
            <mat-label>按案件类型筛选</mat-label>
            <mat-select [(ngModel)]="selectedCaseType" (selectionChange)="onCaseTypeFilterChange()">
              <mat-option [value]="null">
                <em>所有类型</em>
              </mat-option>
              <mat-option *ngFor="let caseType of caseTypes" [value]="caseType">
                {{ caseType }}
              </mat-option>
            </mat-select>
            <mat-hint *ngIf="selectedCaseType">
              {{ selectedCaseType }}: {{ selectedCaseTypeCasesCount }} 个案件
            </mat-hint>
          </mat-form-field>

          <!-- 律师过滤器（仅主任权限可见） -->
          <mat-form-field appearance="outline" class="filter-field" *ngIf="canShowLawyerFilter()">
            <mat-label>按律师筛选</mat-label>
            <mat-select [(ngModel)]="selectedLawyerId" (selectionChange)="onLawyerFilterChange()">
              <mat-option [value]="null">
                <em>所有律师</em>
              </mat-option>
              <mat-option *ngFor="let lawyer of allLawyers" [value]="lawyer.id">
                {{ getUserDisplayName(lawyer) }}
              </mat-option>
            </mat-select>
            <mat-hint *ngIf="selectedLawyerId">
              {{ selectedLawyerName }} 相关案件: {{ selectedLawyerCasesCount }} 个 (包括主律师和协作律师)
            </mat-hint>
            <mat-hint *ngIf="!selectedLawyerId && allLawyers.length === 0 && !lawyersLoading">暂无律师数据</mat-hint>
            <mat-hint *ngIf="lawyersLoading">正在加载律师列表...</mat-hint>
          </mat-form-field>

          <!-- 清除过滤器按钮组 -->
          <div class="filter-actions">
            <button mat-icon-button 
                    *ngIf="selectedCaseType" 
                    (click)="clearCaseTypeFilter()"
                    matTooltip="清除案件类型过滤">
              <mat-icon>clear</mat-icon>
            </button>
            <button mat-icon-button 
                    *ngIf="selectedLawyerId" 
                    (click)="clearLawyerFilter()"
                    matTooltip="清除律师过滤">
              <mat-icon>clear</mat-icon>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 当前过滤结果统计 -->
  <div class="filter-results-info" *ngIf="!isLoading && !error">
    <span class="results-count">
      显示 {{ filteredCasesCount }} / {{ allCasesCount }} 个案件
      <span *ngIf="selectedCaseType" class="filter-info">
        (案件类型: "{{ selectedCaseType }}")
      </span>
      <span *ngIf="selectedLawyerId" class="filter-info">
        (律师: "{{ selectedLawyerName }}")
      </span>
      <span *ngIf="filter === 'mine'" class="filter-info">
        (我的案件)
      </span>
    </span>
  </div>

  <div *ngIf="isLoading" class="loading-spinner">
    <mat-spinner></mat-spinner>
  </div>

  <div *ngIf="error" class="error-message">
    {{ error }}
  </div>

  <table mat-table [dataSource]="dataSource" matSort matSortActive="created_at" matSortDirection="desc" class="mat-elevation-z8" *ngIf="!isLoading && !error">
    <!-- Case Number Column -->
    <ng-container matColumnDef="case_number">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> 案件编号 </th>
      <td mat-cell *matCellDef="let case"> {{case.case_number}} </td>
    </ng-container>

    <!-- Case Cause Column -->
    <ng-container matColumnDef="case_cause">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> 案由 </th>
      <td mat-cell *matCellDef="let case"> {{case.case_cause}} </td>
    </ng-container>

    <!-- Lawyer Column -->
    <ng-container matColumnDef="lawyer">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> 律师 </th>
      <td mat-cell *matCellDef="let case"> {{ getUserDisplayName(case.lawyer) }} </td>
    </ng-container>

    <!-- Status Column -->
    <ng-container matColumnDef="status">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> 状态 </th>
      <td mat-cell *matCellDef="let case">
        <span class="status-badge" [ngClass]="case.status.toLowerCase()">
          {{ getStatusText(case.status) }}
        </span>
        <mat-icon *ngIf="case.is_paid"
                  class="paid-icon"
                  matTooltip="已缴费">
          paid
        </mat-icon>
      </td>
    </ng-container>

    <!-- Parties Column -->
    <ng-container matColumnDef="parties">
      <th mat-header-cell *matHeaderCellDef> 当事人 </th>
      <td mat-cell *matCellDef="let case">
        <div *ngIf="case.parties && case.parties.length > 0" class="parties-list">
          <div *ngFor="let party of case.parties.slice(0, 2)" class="party-item">
            {{ party.party_type_display || getPartyTypeText(party.party_type) }}:
            {{ (party.natural_person?.name || party.legal_entity?.name || '未知名称') }}
          </div>
          <div *ngIf="case.parties.length > 2" class="more-parties">
            +{{ case.parties.length - 2 }} 更多...
          </div>
        </div>
        <div *ngIf="!case.parties || case.parties.length === 0" class="no-parties">
          无当事人信息
        </div>
      </td>
    </ng-container>

    <!-- Created At Column -->
    <ng-container matColumnDef="created_at">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> 创建时间 </th>
      <td mat-cell *matCellDef="let case"> {{case.created_at | date:'yyyy年MM月dd日'}} </td>
    </ng-container>

    <!-- Actions Column -->
    <ng-container matColumnDef="actions">
      <th mat-header-cell *matHeaderCellDef> 操作 </th>
      <td mat-cell *matCellDef="let case">
        <button mat-icon-button [routerLink]="['/cases', case.id]" title="查看详情">
          <mat-icon>visibility</mat-icon>
        </button>
        <button mat-icon-button 
                color="warn"
                *ngIf="canDeleteCase(case)"
                (click)="deleteCase(case)"
                title="删除案件">
          <mat-icon>delete</mat-icon>
        </button>
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
  </table>
</div>

