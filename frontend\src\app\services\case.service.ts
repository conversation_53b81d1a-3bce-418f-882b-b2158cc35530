import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable, tap, map, forkJoin } from 'rxjs';
import { Case, CaseStatus, CaseLawyerCollaboration, User, CaseListResponse } from '../interfaces/case.interface';
import { environment } from '../../environments/environment';
import { TokenService } from './auth.service';
import { jwtDecode } from 'jwt-decode';
import { validateCase, validateCases } from '../utils/data-validation.utils';

// 公章使用登记表相关接口
export interface SealUsageRecord {
  id?: number;
  year: number;
  sequence_number: number;
  date: string;
  matter: string;
  document_name: string;
  quantity: number;
  seal_type: string;
  seal_type_display?: string;
  recipient_unit: string;
  user_name: string;
  approver?: User;
  operator_name: string;
  remarks?: string;
  created_at?: string;
  updated_at?: string;
  last_printed_at?: string;
}

export interface SealUsageRecordCreateDto {
  date: string;
  matter: string;
  document_name: string;
  quantity: number;
  seal_type: string;
  recipient_unit: string;
  user_name: string;
  operator_name: string;
  remarks?: string;
}

// 函件登记表相关接口
export interface LetterRecord {
  id?: number;
  year: number;
  letter_number: number;  // 函数编号
  sequence_number: number; // 审批后的数字序号
  date: string;
  case_number?: string;
  client_name: string;
  quantity: number;
  recipient_unit: string;
  lawyer_name: string;
  letter_type: string;
  letter_type_display?: string;
  approver?: User;
  remarks?: string;
  created_at?: string;
  updated_at?: string;
  last_printed_at?: string;
}

export interface LetterRecordCreateDto {
  date: string;
  case_number?: string;
  client_name: string;
  quantity: number;
  recipient_unit: string;
  lawyer_name: string;
  letter_type: string;
  remarks?: string;
}

@Injectable({
  providedIn: 'root'
})
export class CaseService {
  private apiUrl = `${environment.apiUrl}/cases/`;
  private caseLawyersUrl = `${environment.apiUrl}/case-lawyers/`;
  private sealUsageUrl = `${environment.apiUrl}/seal-usage-records/`;
  private letterRecordUrl = `${environment.apiUrl}/letter-records/`;

  constructor(
    private http: HttpClient,
    private tokenService: TokenService
  ) { }

  getCases(page: number = 1, pageSize: number = 20, filters?: any): Observable<CaseListResponse> {
    const token = this.tokenService.getToken();
    const headers = new HttpHeaders().set(
      'Authorization',
      `Bearer ${token}`
    );

    let params = new HttpParams()
      .set('page', page.toString())
      .set('page_size', pageSize.toString());

    // 添加过滤参数
    if (filters) {
      Object.keys(filters).forEach(key => {
        const value = filters[key];
        if (value !== null && value !== undefined && value !== '') {
          params = params.set(key, value.toString());
        }
      });
    }

    return this.http.get<CaseListResponse>(this.apiUrl, { headers, params }).pipe(
      tap(response => {
        // 验证API返回的数据是否符合客户端接口定义
        validateCases(response.results);
      })
    );
  }

  // 保留原有的getCases方法作为getAllCases，用于需要获取所有案件的场景
  getAllCases(): Observable<Case[]> {
    const token = this.tokenService.getToken();
    const headers = new HttpHeaders().set(
      'Authorization',
      `Bearer ${token}`
    );

    // 使用一个很大的page_size来获取所有数据
    let params = new HttpParams().set('page_size', '1000');

    return this.http.get<CaseListResponse>(this.apiUrl, { headers, params }).pipe(
      tap(response => {
        // 验证API返回的数据是否符合客户端接口定义
        validateCases(response.results);
      }),
      // 只返回results数组，保持向后兼容
      map(response => response.results)
    );
  }

  // 获取案件统计信息
  getCaseStats(): Observable<{totalCases: number, myCases: number}> {
    const token = this.tokenService.getToken();
    const headers = new HttpHeaders().set(
      'Authorization',
      `Bearer ${token}`
    );

    // 分别获取全部案件和我的案件的统计
    const allCasesRequest = this.http.get<CaseListResponse>(this.apiUrl, {
      headers,
      params: new HttpParams()
        .set('page_size', '1')
        .set('scope', 'all')
    });

    const myCasesRequest = this.http.get<CaseListResponse>(this.apiUrl, {
      headers,
      params: new HttpParams()
        .set('page_size', '1')
        .set('scope', 'mine')
    });

    // 使用forkJoin并行请求
    return forkJoin({
      allCases: allCasesRequest,
      myCases: myCasesRequest
    }).pipe(
      map(({ allCases, myCases }) => ({
        totalCases: allCases.count,
        myCases: myCases.count
      }))
    );
  }

  // 从token中获取当前用户信息
  private getCurrentUserFromToken(): any {
    try {
      const token = this.tokenService.getToken();
      if (token) {
        const decoded = jwtDecode(token);
        return decoded;
      }
    } catch (error) {
      console.error('解析token失败:', error);
    }
    return null;
  }

  getCase(id: number): Observable<Case> {
    const token = this.tokenService.getToken();
    const headers = new HttpHeaders().set(
      'Authorization',
      `Bearer ${token}`
    );
    return this.http.get<Case>(`${this.apiUrl}${id}/`, { headers }).pipe(
      tap(caseData => {
        // 验证API返回的数据是否符合客户端接口定义
        validateCase(caseData);
      })
    );
  }

  approveCase(caseId: number, action: string, comment: string, approvalType: string = 'director'): Observable<any> {
    const token = this.tokenService.getToken();
    const headers = new HttpHeaders().set(
      'Authorization',
      `Bearer ${token}`
    );
    const data = {
      action: action,
      comment: comment,
      approval_type: approvalType
    };
    return this.http.post<any>(`${this.apiUrl}${caseId}/approve/`, data, { headers });
  }

  createCase(caseData: any): Observable<Case> {
    const token = this.tokenService.getToken();
    const headers = new HttpHeaders().set(
      'Authorization',
      `Bearer ${token}`
    );
    return this.http.post<Case>(this.apiUrl, caseData, { headers }).pipe(
      tap(createdCase => {
        // 验证API返回的数据是否符合客户端接口定义
        validateCase(createdCase);
      })
    );
  }

  updateStatus(caseId: number, status: CaseStatus): Observable<Case> {
    const token = this.tokenService.getToken();
    const headers = new HttpHeaders().set(
      'Authorization',
      `Bearer ${token}`
    );
    return this.http.post<Case>(
      `${this.apiUrl}${caseId}/update_status/`,
      { status },
      { headers }
    ).pipe(
      tap(updatedCase => {
        // 验证API返回的数据是否符合客户端接口定义
        validateCase(updatedCase);
      })
    );
  }

  // 获取案件的协作律师列表
  getCaseLawyers(caseId: number): Observable<CaseLawyerCollaboration[]> {
    const token = this.tokenService.getToken();
    const headers = new HttpHeaders().set(
      'Authorization',
      `Bearer ${token}`
    );
    return this.http.get<CaseLawyerCollaboration[]>(
      `${this.apiUrl}${caseId}/list_collaborating_lawyers/`,
      { headers }
    );
  }

  // 添加协作律师
  addCollaboratingLawyer(caseId: number, lawyerId: number, feeShareRatio: number = 0, remarks?: string): Observable<Case> {
    const token = this.tokenService.getToken();
    const headers = new HttpHeaders().set(
      'Authorization',
      `Bearer ${token}`
    );
    const data = {
      lawyer_id: lawyerId,
      fee_share_ratio: feeShareRatio,
      remarks: remarks || ''
    };
    return this.http.post<Case>(
      `${this.apiUrl}${caseId}/add_collaborating_lawyer/`,
      data,
      { headers }
    ).pipe(
      tap(updatedCase => {
        validateCase(updatedCase);
      })
    );
  }

  // 移除协作律师
  removeCollaboratingLawyer(caseId: number, lawyerId: number): Observable<Case> {
    const token = this.tokenService.getToken();
    const headers = new HttpHeaders().set(
      'Authorization',
      `Bearer ${token}`
    );
    const data = {
      lawyer_id: lawyerId
    };
    return this.http.post<Case>(
      `${this.apiUrl}${caseId}/remove_collaborating_lawyer/`,
      data,
      { headers }
    ).pipe(
      tap(updatedCase => {
        validateCase(updatedCase);
      })
    );
  }



  // 搜索案件（用于各种组件中的案件关联）
  searchCases(keyword: string): Observable<Case[]> {
    const token = this.tokenService.getToken();
    const headers = new HttpHeaders().set(
      'Authorization',
      `Bearer ${token}`
    );
    const params = new HttpParams().set('keyword', keyword);
    return this.http.get<Case[]>(`${this.apiUrl}search_cases/`, { headers, params });
  }

  // 更新案件属性
  updateCase(caseId: number, caseData: Partial<Case>): Observable<Case> {
    const token = this.tokenService.getToken();
    const headers = new HttpHeaders().set(
      'Authorization',
      `Bearer ${token}`
    );
    return this.http.patch<Case>(
      `${this.apiUrl}${caseId}/`,
      caseData,
      { headers }
    ).pipe(
      tap(updatedCase => {
        validateCase(updatedCase);
      })
    );
  }

  // 行政人员标记案件为已缴费
  markAsPaid(caseId: number): Observable<any> {
    const token = this.tokenService.getToken();
    const headers = new HttpHeaders().set(
      'Authorization',
      `Bearer ${token}`
    );
    return this.http.post<any>(
      `${this.apiUrl}${caseId}/mark_as_paid/`,
      {},
      { headers }
    );
  }

  // 更新协作律师信息
  updateCaseLawyerCollaboration(collaborationId: number, data: Partial<CaseLawyerCollaboration>): Observable<CaseLawyerCollaboration> {
    const token = this.tokenService.getToken();
    const headers = new HttpHeaders().set(
      'Authorization',
      `Bearer ${token}`
    );
    return this.http.patch<CaseLawyerCollaboration>(
      `${this.caseLawyersUrl}${collaborationId}/`,
      data,
      { headers }
    );
  }

  // 公章使用登记表相关方法
  getSealUsageRecords(params: {
    year?: number;
    start_date?: string;
    end_date?: string;
    user_name?: string;
    approver?: number;
    operator_name?: string;
    seal_type?: string;
    keyword?: string;
  } = {}): Observable<SealUsageRecord[]> {
    const token = this.tokenService.getToken();
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);

    let httpParams = new HttpParams();
    Object.keys(params).forEach(key => {
      const value = params[key as keyof typeof params];
      if (value !== undefined && value !== null && value !== '') {
        httpParams = httpParams.set(key, value.toString());
      }
    });

    return this.http.get<SealUsageRecord[]>(this.sealUsageUrl, { headers, params: httpParams });
  }

  getSealUsageRecord(id: number): Observable<SealUsageRecord> {
    const token = this.tokenService.getToken();
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.get<SealUsageRecord>(`${this.sealUsageUrl}${id}/`, { headers });
  }

  createSealUsageRecord(record: SealUsageRecordCreateDto): Observable<SealUsageRecord> {
    const token = this.tokenService.getToken();
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post<SealUsageRecord>(this.sealUsageUrl, record, { headers });
  }

  updateSealUsageRecord(id: number, record: Partial<SealUsageRecordCreateDto>): Observable<SealUsageRecord> {
    const token = this.tokenService.getToken();
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.patch<SealUsageRecord>(`${this.sealUsageUrl}${id}/`, record, { headers });
  }

  deleteSealUsageRecord(id: number): Observable<void> {
    const token = this.tokenService.getToken();
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.delete<void>(`${this.sealUsageUrl}${id}/`, { headers });
  }

  approveSealUsageRecord(id: number): Observable<SealUsageRecord> {
    const token = this.tokenService.getToken();
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post<SealUsageRecord>(`${this.sealUsageUrl}${id}/approve/`, {}, { headers });
  }

  printSealUsageRecord(id: number, page: number = 1): Observable<{
    records: SealUsageRecord[];
    total_pages: number;
    current_page: number;
    year: number;
  }> {
    const token = this.tokenService.getToken();
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post<{
      records: SealUsageRecord[];
      total_pages: number;
      current_page: number;
      year: number;
    }>(`${this.sealUsageUrl}${id}/print_record/`, { page }, { headers });
  }

  printSealUsageRecordsByYear(year: number, page: number = 1): Observable<{
    records: SealUsageRecord[];
    total_pages: number;
    current_page: number;
    year: number;
  }> {
    const token = this.tokenService.getToken();
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    const params = new HttpParams().set('year', year.toString()).set('page', page.toString());
    return this.http.get<{
      records: SealUsageRecord[];
      total_pages: number;
      current_page: number;
      year: number;
    }>(`${this.sealUsageUrl}print_year/`, { headers, params });
  }

  openSealUsagePrintWindow(data: {
    records: SealUsageRecord[];
    total_pages: number;
    current_page: number;
    year: number;
  }): void {
    // 编码数据以便通过URL传递
    const encodedData = encodeURIComponent(JSON.stringify(data));

    // 打开新窗口用于打印
    window.open(`${environment.apiUrl}/templates/cases/print_seal_usage.html?data=${encodedData}`, '_blank');
  }

  // 函件登记表相关方法
  getLetterRecords(params: {
    year?: number;
    start_date?: string;
    end_date?: string;
    letter_type?: string;
    approver?: number;
    keyword?: string;
  } = {}): Observable<LetterRecord[]> {
    const token = this.tokenService.getToken();
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);

    let httpParams = new HttpParams();
    Object.keys(params).forEach(key => {
      const value = params[key as keyof typeof params];
      if (value !== undefined && value !== null && value !== '') {
        httpParams = httpParams.set(key, value.toString());
      }
    });

    return this.http.get<LetterRecord[]>(this.letterRecordUrl, { headers, params: httpParams });
  }

  getLetterRecord(id: number): Observable<LetterRecord> {
    const token = this.tokenService.getToken();
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.get<LetterRecord>(`${this.letterRecordUrl}${id}/`, { headers });
  }

  createLetterRecord(record: LetterRecordCreateDto): Observable<LetterRecord> {
    const token = this.tokenService.getToken();
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post<LetterRecord>(this.letterRecordUrl, record, { headers });
  }

  updateLetterRecord(id: number, record: Partial<LetterRecordCreateDto>): Observable<LetterRecord> {
    const token = this.tokenService.getToken();
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.patch<LetterRecord>(`${this.letterRecordUrl}${id}/`, record, { headers });
  }

  deleteLetterRecord(id: number): Observable<void> {
    const token = this.tokenService.getToken();
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.delete<void>(`${this.letterRecordUrl}${id}/`, { headers });
  }

  approveLetterRecord(id: number): Observable<LetterRecord> {
    const token = this.tokenService.getToken();
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    return this.http.post<LetterRecord>(`${this.letterRecordUrl}${id}/approve/`, {}, { headers });
  }

  printLetterRecord(id: number): void {
    // 直接打开Django模板打印页面
    window.open(`${environment.apiUrl}/templates/cases/print_letter_record/${id}/`, '_blank');
  }

  printLetterRecordsByYear(year: number, letterType: string, page: number = 1): Observable<{
    records: LetterRecord[];
    total_pages: number;
    current_page: number;
    year: number;
    letter_type: string;
  }> {
    const token = this.tokenService.getToken();
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
    const params = new HttpParams()
      .set('year', year.toString())
      .set('letter_type', letterType)
      .set('page', page.toString());
    return this.http.get<{
      records: LetterRecord[];
      total_pages: number;
      current_page: number;
      year: number;
      letter_type: string;
    }>(`${this.letterRecordUrl}print_year/`, { headers, params });
  }

  openLetterPrintWindow(data: {
    records: LetterRecord[];
    total_pages: number;
    current_page: number;
    year: number;
    letter_type: string;
  }): void {
    // 防御性编程：检查必要字段是否存在
    if (!data || !data.records || !data.year || !data.letter_type) {
      console.error('打印数据不完整:', data);
      return;
    }

    // 编码数据以便通过URL传递
    const recordsData = encodeURIComponent(JSON.stringify(data.records));
    const params = new URLSearchParams({
      records: recordsData,
      year: data.year.toString(),
      letter_type: data.letter_type,
      current_page: (data.current_page || 1).toString(),
      total_pages: (data.total_pages || 1).toString()
    });

    // 打开新窗口用于打印
    window.open(`${environment.apiUrl}/templates/cases/print_letter.html?${params.toString()}`, '_blank');
  }

  // 删除案件
  deleteCase(caseId: number): Observable<any> {
    const token = this.tokenService.getToken();
    const headers = new HttpHeaders().set(
      'Authorization',
      `Bearer ${token}`
    );
    return this.http.delete(
      `${this.apiUrl}${caseId}/`,
      { headers }
    );
  }
}
