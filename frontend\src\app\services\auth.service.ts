import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpParams } from '@angular/common/http';
import { Router } from '@angular/router';
import { environment } from '../../environments/environment';
import { tap, catchError, switchMap } from 'rxjs/operators';
import { BehaviorSubject, throwError, Observable } from 'rxjs';
import { jwtDecode } from 'jwt-decode';

interface UserProfile {
  has_admin_approval_permission: boolean;
  has_director_approval_permission: boolean;
  has_finance_permission: boolean;
  is_hidden: boolean;
}

interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  profile: UserProfile;
  permissions: string[];
}

interface JwtPayload {
  user_id: number;
  username: string;
  email?: string;
  exp: number;
  first_name?: string;
  last_name?: string;
  // ... 其他 JWT 字段
}

@Injectable({
  providedIn: 'root'
})
export class TokenService {
  private tokenExpiredSubject = new BehaviorSubject<boolean>(false);
  tokenExpired$ = this.tokenExpiredSubject.asObservable();

  getToken(): string | null {
    let token = localStorage.getItem('token');

    if (!token) {
      // 获取 Cookie 内令牌（由django views设置）
      const cookies = document.cookie.split(';');
      for (const cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'auth_token') {
          token = value;
        }
      }
      if (!token) {
        // console.error('No token found');
        return null;
      }

      // 保存到localStorage
      localStorage.setItem('token', token);
      console.debug('Token found in cookie, saved to localStorage', token);
    }

    return token;
  }

  getRefreshToken(): string | null {
    return localStorage.getItem('refresh');
  }

  setToken(token: string): void {
    localStorage.setItem('token', token);
    this.tokenExpiredSubject.next(false);
  }

  setRefreshToken(token: string): void {
    localStorage.setItem('refresh', token);
  }

  removeTokens(): void {
    localStorage.removeItem('token');
    localStorage.removeItem('refresh');
    this.tokenExpiredSubject.next(true);
  }

  isTokenExpired(token: string | null): boolean {
    if (!token) return true;

    try {
      const decoded = jwtDecode<JwtPayload>(token);
      const expirationDate = new Date(decoded.exp * 1000);
      const now = new Date();
      now.setMinutes(now.getMinutes() + 5);
      return now > expirationDate;
    } catch {
      return true;
    }
  }
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private apiUrl = environment.apiUrl;
  private userSubject = new BehaviorSubject<User | null>(null);
  private refreshTokenInProgress = false;
  private refreshTokenSubject = new BehaviorSubject<string | null>(null);
  user$ = this.userSubject.asObservable();

  constructor(
    private http: HttpClient,
    private router: Router,
    private tokenService: TokenService
  ) {
    const token = this.tokenService.getToken();
    if (token) {
      // 直接获取完整的用户信息，而不是从token中创建基本对象
      this.getCurrentUser().subscribe();
    }

    // 监听 token 过期事件
    this.tokenService.tokenExpired$.subscribe(expired => {
      if (expired) {
        this.handleLogout();
      }
    });
  }

  login(username: string, password: string) {
    return this.http.post<{ access: string, refresh: string }>(`${this.apiUrl}/token/`, { username, password })
      .pipe(
        tap(response => {
          this.tokenService.setToken(response.access);
          this.tokenService.setRefreshToken(response.refresh);
          // 直接获取完整的用户信息，而不是从token中创建基本对象
          this.getCurrentUser().subscribe();
        })
      );
  }

  private decodeToken(token: string): JwtPayload | null {
    try {
      const decoded = jwtDecode<JwtPayload>(token);
      console.log('Token 内容:', {
        ...decoded,
        token_type: 'Bearer',
        raw_token: token
      });
      return decoded;
    } catch (error) {
      console.error('Error decoding token:', error);
      return null;
    }
  }

  getToken(): string | null {
    return this.tokenService.getToken();
  }

  isLoggedIn(): boolean {
    const token = this.getToken();
    if (!token) return false;
    return !this.tokenService.isTokenExpired(token);
  }

  private handleLogout() {
    this.userSubject.next(null);
    this.router.navigate(['/login']);
  }

  logout() {
    // 清除localStorage中的令牌
    this.tokenService.removeTokens();
    localStorage.removeItem('user');
    
    // 清除所有cookie
    this.clearAllCookies();
    
    // 处理登出后的操作
    this.handleLogout();
  }

  /**
   * 清除所有cookie
   */
  private clearAllCookies() {
    const cookies = document.cookie.split(';');
    
    for (const cookie of cookies) {
      const eqPos = cookie.indexOf('=');
      const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
      
      // 设置过期时间为过去的时间来删除cookie
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
      
      // 同时考虑不同路径的cookie
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=${window.location.hostname}`;
    }
    
    console.log('所有cookie已清除');
  }

  refreshToken(): Observable<{ access: string }> {
    if (this.refreshTokenInProgress) {
      return new Observable(observer => {
        this.refreshTokenSubject.subscribe(token => {
          if (token) {
            observer.next({ access: token });
            observer.complete();
          }
        });
      });
    }
    this.refreshTokenInProgress = true;
    this.refreshTokenSubject.next(null);

    const refresh = this.tokenService.getRefreshToken();
    return this.http.post<{ access: string }>(`${this.apiUrl}/api/token/refresh/`, { refresh })
      .pipe(
        tap(response => {
          this.refreshTokenInProgress = false;
          this.refreshTokenSubject.next(response.access);
          this.tokenService.setToken(response.access);
          const decodedToken = this.decodeToken(response.access);
          if (decodedToken) {
            // 创建一个基本的 User 对象
            const basicUser = {
              id: decodedToken.user_id,
              username: decodedToken.username,
              email: decodedToken.email || '',
              first_name: '',
              last_name: '',
              profile: { 
                has_admin_approval_permission: false,
                has_director_approval_permission: false,
                has_finance_permission: false,
                is_hidden: false
              },
              permissions: []
            };
            this.userSubject.next(basicUser);

            // 获取完整的用户信息
            this.getCurrentUser().subscribe({
              error: (error) => {
                console.error('刷新token后获取用户信息失败:', error);
              }
            });
          }
        }),
        catchError((error: HttpErrorResponse) => {
          this.refreshTokenInProgress = false;
          if (error.status === 401) {
            // refresh token 也过期了，需要重新登录
            this.logout();
          }
          return throwError(() => error);
        })
      );
  }

  checkAuthStatus() {
    // 使用/api/users/me/接口来检查认证状态，代替不存在的/auth/check-status
    return this.http.get<any>(`${this.apiUrl}/users/me/`).pipe(
      catchError(error => {
        if (error.status === 401) {
          this.logout();
        }
        return throwError(() => error);
      })
    );
  }

  startAuthStatusCheck() {
    // 每5分钟检查一次登录状态
    setInterval(() => {
      this.checkAuthStatus().subscribe();
    }, 5 * 60 * 1000);
  }

  getCurrentUser(): Observable<User> {
    const fetchUser = () => this.http.get<User>(`${this.apiUrl}/users/me/`).pipe(
      tap(user => {
        console.log('获取到的用户信息:', user);
        this.userSubject.next(user);
      })
    );

    return fetchUser().pipe(
      catchError(error => {
        console.error('获取用户信息失败:', error);
        // 如果是401错误，可能是token过期，尝试刷新token
        if (error.status === 401) {
          // 先刷新token，然后重新获取用户信息
          return this.refreshToken().pipe(
            // 刷新token成功后，重新获取用户信息
            switchMap(() => fetchUser())
          );
        }
        return throwError(() => error);
      })
    );
  }

  // 判断用户是否有任何审批权限
  canApproveCases(): boolean {
    const user = this.userSubject.value;
    // 只检查新版权限
    return !!(user?.profile?.has_admin_approval_permission || 
              user?.profile?.has_director_approval_permission);
  }

  // 检查是否有行政审批权限
  canAdminApproveCases(): boolean {
    const user = this.userSubject.value;
    // 只检查行政审批权限
    return !!user?.profile?.has_admin_approval_permission;
  }

  // 检查是否有主任审批权限
  canDirectorApproveCases(): boolean {
    const user = this.userSubject.value;
    // 只检查主任审批权限
    return !!user?.profile?.has_director_approval_permission;
  }

  // 检查是否有特定权限
  hasPermission(permission: string): boolean {
    const user = this.userSubject.value;
    return user?.permissions?.includes(permission) || false;
  }

  // 获取所有用户（律师）
  getAllUsers(includeHidden: boolean = false): Observable<User[]> {
    let params = new HttpParams();
    if (includeHidden) {
      params = params.set('allusers', 'true');
    }
    
    return this.http.get<User[]>(`${this.apiUrl}/users/`, { params });
  }
}
