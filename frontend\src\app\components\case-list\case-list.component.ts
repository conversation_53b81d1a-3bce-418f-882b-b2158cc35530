import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { RouterModule } from '@angular/router';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatSort, MatSortModule, Sort } from '@angular/material/sort';
import { MatPaginator, MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { take } from 'rxjs/operators';

import { CaseService } from '../../services/case.service';
import { AuthService } from '../../services/auth.service';
import { Case, CaseStatus, User, CaseListResponse } from '../../interfaces/case.interface';
import { getUserDisplayName } from '../../utils/user.utils';
import { getPartyTypeText } from '../../utils/party.utils';

@Component({
  selector: 'app-case-list',
  templateUrl: './case-list.component.html',
  styleUrls: ['./case-list.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatTableModule,
    MatProgressSpinnerModule,
    MatIconModule,
    MatButtonModule,
    MatButtonToggleModule,
    MatSelectModule,
    MatFormFieldModule,
    RouterModule,
    MatTooltipModule,
    MatSnackBarModule,
    MatSortModule,
    MatPaginatorModule
  ]
})
export class CaseListComponent implements OnInit {
  dataSource = new MatTableDataSource<Case>([]);
  @ViewChild(MatSort) sort!: MatSort;
  @ViewChild(MatPaginator) paginator!: MatPaginator;

  // 分页相关属性
  totalCount = 0;
  currentPage = 1;
  pageSize = 20;
  pageSizeOptions = [10, 20, 50, 100];

  // 统计数据
  totalCasesCount = 0;
  myCasesCountFromAPI = 0;

  cases: Case[] = [];
  filteredCases: Case[] = [];
  displayedColumns: string[] = [
    'case_number',
    'case_cause',
    'lawyer',
    'status',
    'parties',
    'created_at',
    'actions'
  ];
  isLoading = true;
  error: string | null = null;
  filter: 'all' | 'mine' = 'all';
  currentUsername: string | null = null;
  currentUserId: number | null = null;

  // 律师过滤器相关
  allLawyers: User[] = [];
  selectedLawyerId: number | null = null;
  lawyersLoading = false;

  // 案件类型过滤器相关
  caseTypes = [
    '民事案件',
    '刑事案件',
    '行政案件',
    '其他案件',
    '未设置'
  ];
  selectedCaseType: string | null = null;

  // 导出工具函数供模板使用
  getUserDisplayName = getUserDisplayName;
  getPartyTypeText = getPartyTypeText;

  constructor(
    private caseService: CaseService,
    private authService: AuthService,
    private snackBar: MatSnackBar
  ) {
    this.authService.user$.subscribe(user => {
      this.currentUsername = user?.username || null;
      this.currentUserId = user?.id || null;

      // 当用户信息更新时，重新检查是否需要加载律师列表
      // 只有当用户信息存在、用户有权限、尚未加载且不在加载中时才加载
      if (user && user.profile && this.allLawyers.length === 0 && !this.lawyersLoading) {
        const hasDirectorPermission = user.profile.has_director_approval_permission;
        if (hasDirectorPermission) {
          this.loadAllLawyers();
        }
      }

      this.applyFilter();
    });
  }

  ngOnInit(): void {
    this.loadCases();
    this.loadCaseStats();
    // 律师列表的加载现在在用户信息更新时自动处理，无需在这里检查

    // 设置自定义排序逻辑
    this.dataSource.sortingDataAccessor = (item: Case, property: string) => {
      switch(property) {
        case 'lawyer':
          return item.lawyer ? this.getUserDisplayName(item.lawyer).toLowerCase() : '';
        case 'status':
          return item.status ? this.getStatusText(item.status).toLowerCase() : '';
        case 'created_at':
          return item.created_at ? new Date(item.created_at).getTime() : 0;
        case 'case_number':
          return item.case_number ? item.case_number.toLowerCase() : '';
        case 'case_cause':
          return item.case_cause ? item.case_cause.toLowerCase() : '';
        default:
          const value = item[property as keyof Case];
          return value !== null && value !== undefined ? String(value).toLowerCase() : '';
      }
    };
  }

  ngAfterViewInit() {
    this.dataSource.sort = this.sort;

    // Force the sort to be applied after the view is initialized
    if (this.sort) {
      this.sort.sortChange.subscribe(() => {
        console.log('Sort changed:', this.sort.active, this.sort.direction);
      });
    }
  }

  loadCases(page: number = 1, pageSize: number = this.pageSize): void {
    this.isLoading = true;

    // 构建过滤参数
    const filters: any = {};

    // 注意：由于后端已经根据用户权限过滤了案件，这里暂时不需要额外的过滤
    // 如果需要更复杂的过滤，可以在这里添加

    this.caseService.getCases(page, pageSize, filters).subscribe({
      next: (response: CaseListResponse) => {
        this.cases = response.results;
        this.totalCount = response.count;
        this.currentPage = page;
        this.pageSize = pageSize;

        // 更新数据源
        this.dataSource.data = this.cases;
        this.filteredCases = this.cases; // 保持兼容性
        this.isLoading = false;

        // Ensure sort is applied after data is loaded
        setTimeout(() => {
          if (this.sort && this.dataSource.sort !== this.sort) {
            this.dataSource.sort = this.sort;
          }
        });
      },
      error: (error) => {
        this.error = '加载案件列表失败';
        this.isLoading = false;
        console.error('加载案件列表出错:', error);
      }
    });
  }

  loadCaseStats(): void {
    this.caseService.getCaseStats().subscribe({
      next: (stats) => {
        this.totalCasesCount = stats.totalCases;
        // 对于"我的案件"数量，我们需要基于当前用户ID来计算
        // 这里先设置为总数，后续在用户信息加载后会重新计算
        this.myCasesCountFromAPI = stats.myCases;
      },
      error: (error) => {
        console.error('加载案件统计失败:', error);
      }
    });
  }

  onFilterChange(): void {
    // 重置到第一页并重新加载数据
    this.currentPage = 1;
    this.loadCases(this.currentPage, this.pageSize);
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex + 1; // Angular Material的pageIndex从0开始
    this.pageSize = event.pageSize;
    this.loadCases(this.currentPage, this.pageSize);
  }

  private applyFilter(): void {
    // 现在过滤主要通过重新加载数据来实现
    // 保留一些前端过滤逻辑用于显示统计信息
    let filteredData = this.cases;

    // 应用全部案件/我的案件过滤（用于统计）
    if (this.filter === 'mine' && this.currentUserId) {
      filteredData = filteredData.filter(
        item => item.lawyer.id === this.currentUserId
      );
    }

    // 应用律师过滤（用于统计）
    if (this.canShowLawyerFilter() && this.selectedLawyerId) {
      filteredData = filteredData.filter(caseItem => {
        if (caseItem.lawyer.id === this.selectedLawyerId) {
          return true;
        }
        if (caseItem.collaborating_lawyers && caseItem.collaborating_lawyers.length > 0) {
          return caseItem.collaborating_lawyers.some(lawyer => lawyer.id === this.selectedLawyerId);
        }
        return false;
      });
    }

    // 应用案件类型过滤（用于统计）
    if (this.selectedCaseType) {
      filteredData = filteredData.filter(caseItem => {
        const caseType = this.getCaseType(caseItem);
        return caseType === this.selectedCaseType;
      });
    }

    this.filteredCases = filteredData;
    // 不再调用updateDataSource，因为数据已经在loadCases中更新了
  }

  private updateDataSource(): void {
    this.dataSource.data = this.filteredCases;

    // If sort is already initialized, maintain the current sort
    if (this.sort && this.sort.active) {
      const currentSort: Sort = {
        active: this.sort.active,
        direction: this.sort.direction
      };

      // Apply the current sort
      this.sort.sort({
        id: currentSort.active,
        start: currentSort.direction,
        disableClear: false
      });
    }
  }

  getStatusText(status: CaseStatus): string {
    const statusMap: Record<CaseStatus, string> = {
      [CaseStatus.CREATED]: '创建',
      [CaseStatus.APPLYING]: '申请中',
      [CaseStatus.ADMIN_REVIEWING]: '行政审批中',
      [CaseStatus.DIRECTOR_REVIEWING]: '主任审批中',
      [CaseStatus.PROCESSING_DELEGATION]: '办理委托手续中',
      [CaseStatus.IN_PROGRESS]: '办案中',
      [CaseStatus.APPLYING_CLOSURE]: '申请结案中',
      [CaseStatus.CLOSURE_APPROVED]: '已通过主任审批结案',
      [CaseStatus.ARCHIVED]: '已归档'
    };
    return statusMap[status] || status;
  }

  getLawyerName(lawyer: any): string {
    if (!lawyer) return '未知律师';
    if (lawyer.last_name || lawyer.first_name) {
      return `${lawyer.last_name || ''}${lawyer.first_name || ''}`.trim();
    }
    return lawyer.username || `律师ID: ${lawyer.id}`;
  }

  /**
   * 获取全部案件总数
   */
  get allCasesCount(): number {
    return this.totalCasesCount;
  }

  /**
   * 获取我的案件总数
   */
  get myCasesCount(): number {
    return this.myCasesCountFromAPI;
  }

  /**
   * 获取当前过滤条件下的案件总数
   */
  get filteredCasesCount(): number {
    return this.totalCount;
  }

  /**
   * 获取选中律师的案件数量（包括主律师和协作律师的案件）
   */
  get selectedLawyerCasesCount(): number {
    if (!this.selectedLawyerId || !Array.isArray(this.cases)) {
      return 0;
    }

    return this.cases.filter(caseItem => {
      // 检查主律师
      if (caseItem.lawyer.id === this.selectedLawyerId) {
        return true;
      }
      // 检查协作律师
      if (caseItem.collaborating_lawyers && caseItem.collaborating_lawyers.length > 0) {
        return caseItem.collaborating_lawyers.some(lawyer => lawyer.id === this.selectedLawyerId);
      }
      return false;
    }).length;
  }

  /**
   * 获取选中律师的显示名称
   */
  get selectedLawyerName(): string {
    if (!this.selectedLawyerId) return '';
    const selectedLawyer = this.allLawyers.find(lawyer => lawyer.id === this.selectedLawyerId);
    return selectedLawyer ? this.getUserDisplayName(selectedLawyer) : '';
  }

  /**
   * 检查当前用户是否可以看到律师过滤器（主任权限）
   */
  canShowLawyerFilter(): boolean {
    // 直接检查当前用户信息，而不依赖AuthService的异步方法
    let hasPermission = false;
    this.authService.user$.pipe(take(1)).subscribe(user => {
      if (user && user.profile) {
        hasPermission = !!user.profile.has_director_approval_permission;
      }
    });

    return hasPermission;
  }

  /**
   * 加载所有律师列表
   */
  loadAllLawyers(): void {
    this.lawyersLoading = true;
    this.authService.getAllUsers().subscribe({
      next: (users) => {
        this.allLawyers = users.sort((a, b) =>
          this.getUserDisplayName(a).localeCompare(this.getUserDisplayName(b))
        );
        this.lawyersLoading = false;
      },
      error: (error) => {
        console.error('加载律师列表失败:', error);
        this.snackBar.open('加载律师列表失败', '关闭', { duration: 3000 });
        this.lawyersLoading = false;
      }
    });
  }

  /**
   * 律师过滤器变化事件
   */
  onLawyerFilterChange(): void {
    console.log('Lawyer filter changed to:', this.selectedLawyerId);
    this.applyFilter();
  }

  /**
   * 清除律师过滤器
   */
  clearLawyerFilter(): void {
    this.selectedLawyerId = null;
    this.applyFilter();
  }

  /**
   * 获取案件类型
   */
  getCaseType(caseItem: Case): string {
    const caseType = caseItem.content?.['案件类型'];
    if (!caseType || caseType.trim() === '') {
      return '未设置';
    }
    return caseType;
  }

  /**
   * 获取选中案件类型的案件数量
   */
  get selectedCaseTypeCasesCount(): number {
    if (!this.selectedCaseType || !Array.isArray(this.cases)) return 0;

    let casesToCount = this.cases;

    // 如果同时选择了"我的案件"，先应用这个过滤
    if (this.filter === 'mine' && this.currentUserId) {
      casesToCount = casesToCount.filter(item => item.lawyer.id === this.currentUserId);
    }

    // 如果同时选择了律师过滤，再应用律师过滤
    if (this.canShowLawyerFilter() && this.selectedLawyerId) {
      casesToCount = casesToCount.filter(caseItem => {
        if (caseItem.lawyer.id === this.selectedLawyerId) {
          return true;
        }
        if (caseItem.collaborating_lawyers && caseItem.collaborating_lawyers.length > 0) {
          return caseItem.collaborating_lawyers.some(lawyer => lawyer.id === this.selectedLawyerId);
        }
        return false;
      });
    }

    // 最后应用案件类型过滤
    return casesToCount.filter(caseItem => {
      const caseType = this.getCaseType(caseItem);
      return caseType === this.selectedCaseType;
    }).length;
  }

  /**
   * 案件类型过滤器变化事件
   */
  onCaseTypeFilterChange(): void {
    console.log('Case type filter changed to:', this.selectedCaseType);
    this.applyFilter();
  }

  /**
   * 清除案件类型过滤器
   */
  clearCaseTypeFilter(): void {
    this.selectedCaseType = null;
    this.applyFilter();
  }

  /**
   * 检查是否可以删除案件
   */
  canDeleteCase(caseItem: Case): boolean {
    // 检查是否是案件创建者
    if (!this.currentUserId || caseItem.lawyer.id !== this.currentUserId) {
      return false;
    }

    // 只有在主任审批前可以删除（创建、申请中、行政审批中）
    return caseItem.status === CaseStatus.CREATED ||
           caseItem.status === CaseStatus.APPLYING ||
           caseItem.status === CaseStatus.ADMIN_REVIEWING;
  }

  /**
   * 删除案件
   */
  deleteCase(caseItem: Case): void {
    if (!this.canDeleteCase(caseItem)) {
      return;
    }

    const confirmMessage = `确定要删除案件"${caseItem.case_cause}"吗？此操作不可撤销！`;

    if (confirm(confirmMessage)) {
      this.caseService.deleteCase(caseItem.id).subscribe({
        next: () => {
          this.snackBar.open('案件删除成功', '关闭', {
            duration: 3000,
          });
          // 删除成功后重新加载案件列表
          this.loadCases();
        },
        error: (error) => {
          const errorMessage = error.error?.error || '删除案件失败';
          this.snackBar.open(errorMessage, '关闭', {
            duration: 3000,
          });
          console.error('删除案件出错:', error);
        }
      });
    }
  }
}


