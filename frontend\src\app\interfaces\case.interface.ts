export enum CaseStatus {
  CREATED = 'CREATED',                   // 创建
  APPLYING = 'APPLYING',                 // 申请中
  ADMIN_REVIEWING = 'ADMIN_REVIEWING',   // 行政审批中
  DIRECTOR_REVIEWING = 'DIRECTOR_REVIEWING', // 主任审批中
  PROCESSING_DELEGATION = 'PROCESSING_DELEGATION', // 办理委托手续中
  IN_PROGRESS = 'IN_PROGRESS',           // 办案中
  APPLYING_CLOSURE = 'APPLYING_CLOSURE', // 申请结案中
  CLOSURE_APPROVED = 'CLOSURE_APPROVED', // 已通过主任审批结案
  ARCHIVED = 'ARCHIVED'                  // 已归档
}

export enum PartyType {
  PLAINTIFF = 'PLAINTIFF',       // 原告
  DEFENDANT = 'DEFENDANT',       // 被告
  THIRD_PARTY = 'THIRD_PARTY',   // 第三人
  SUSPECT = 'SUSPECT',           // 犯罪嫌疑人
  SUSPECT_FAMILY = 'SUSPECT_FAMILY',  // 嫌疑人家属
  NON_LITIGATION_CLIENT = 'NON_LITIGATION_CLIENT'  // 非诉委托人
}

export interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
}

export interface CaseLawyerCollaboration {
  id: number;
  case: number;
  lawyer: User;
  fee_share_ratio: number;
  joined_at: string;
  remarks?: string;
}

export interface CaseApproval {
  id: number;
  approver: User;
  action: 'approve' | 'reject' | 'revise';
  action_display: string;
  comment: string;
  created_at: string;
  status_when_approved: CaseStatus;
}

export interface CaseContent {
  case_summary: string;
  legal_basis: string;
  case_analysis: string;
  evidence: string;
  judgment: string;
  comments: string;
  [key: string]: string;  // 添加索引签名
}

export interface Case {
  id: number;
  case_number: string | null;
  case_cause: string;
  lawyer: User;
  collaborating_lawyers?: User[];
  status: CaseStatus;
  status_display: string;
  is_paid: boolean;
  is_sensitive: boolean;
  is_risk_agency: boolean;
  created_at: string;
  updated_at: string;
  content: any;
  parties?: CaseParty[];
  approvals?: CaseApproval[];
  natural_person_id?: number; // 自然人ID
  legal_entity_id?: number; // 法人ID
  agreed_lawyer_fee?: number; // 商定律师费
  clients?: ClientInfo[]; // 委托人信息（用于搜索结果）
}

export interface ClientInfo {
  id: number;
  name: string;
  type: string; // '自然人' 或 '法人'
  id_number: string; // 身份证号码或法人代表身份证号码
}

export interface CaseParty {
  id: number;
  party_type: PartyType;
  party_type_display?: string; // 添加party_type_display字段
  is_client: boolean;          // 添加is_client字段，标记是否为委托人
  internal_number?: string;    // 添加internal_number字段
  remarks?: string;            // 添加remarks字段
  natural_person?: {
    id: number;
    name: string;
    id_number?: string;
    phone_number?: string;
    remarks?: string;
  };
  legal_entity?: {
    id: number;
    name: string;
    credit_code?: string;
    representative_name?: string;
    representative_id_number?: string;
    representative_phone_number?: string;
    remarks?: string;
  };
}
