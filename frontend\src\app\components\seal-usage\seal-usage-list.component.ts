import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { DatePipe, Ng<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>If, DecimalPipe } from '@angular/common';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatSortModule, Sort } from '@angular/material/sort';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTabsModule } from '@angular/material/tabs';
import { CaseService, SealUsageRecord, LetterRecord } from '../../services/case.service';
import { AuthService } from '../../services/auth.service';
import { getUserDisplayName } from '../../utils/user.utils';
import { SealUsageDialogComponent } from './seal-usage-dialog.component';
import { LetterDialogComponent } from './letter-dialog.component';
import { FirmInfoService } from '../../services/firm-info.service';

// 定义User接口
interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  profile: {
    has_admin_approval_permission: boolean;
    has_director_approval_permission: boolean;
  };
  permissions: string[];
}

@Component({
  selector: 'app-seal-usage-list',
  standalone: true,
  imports: [
    NgIf, NgFor, NgClass, DatePipe, DecimalPipe,
    ReactiveFormsModule,
    MatTableModule, MatPaginatorModule, MatSortModule,
    MatFormFieldModule, MatInputModule, MatButtonModule,
    MatIconModule, MatCardModule, MatDatepickerModule,
    MatNativeDateModule, MatSelectModule, MatDialogModule,
    MatTooltipModule, MatSnackBarModule, MatTabsModule
  ],
  providers: [DatePipe],
  template: `
    <div class="container">
      <div class="header">
        <h1>登记表管理</h1>
      </div>

      <mat-tab-group [(selectedIndex)]="selectedTabIndex" (selectedTabChange)="onTabChange($event)">
        <!-- 公章使用登记表 Tab -->
        <mat-tab label="公章使用登记表">
          <div class="tab-content">
            <div class="tab-header">
              <h2>公章使用登记表</h2>
              <button mat-raised-button color="primary" (click)="openSealUsageDialog()">
                <mat-icon>add</mat-icon> 新增记录
              </button>
            </div>

      <mat-card class="filter-section">
        <mat-card-content>
          <form [formGroup]="filterForm">
            <div class="filter-row">
              <mat-form-field appearance="outline">
                <mat-label>关键词</mat-label>
                <input matInput formControlName="keyword" placeholder="搜索事由、文件名称等...">
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>年份</mat-label>
                <mat-select formControlName="year">
                  <mat-option [value]="">全部</mat-option>
                  <mat-option *ngFor="let year of availableYears" [value]="year">{{ year }}</mat-option>
                </mat-select>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>开始日期</mat-label>
                <input matInput [matDatepicker]="startPicker" formControlName="startDate">
                <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
                <mat-datepicker #startPicker></mat-datepicker>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>结束日期</mat-label>
                <input matInput [matDatepicker]="endPicker" formControlName="endDate">
                <mat-datepicker-toggle matSuffix [for]="endPicker"></mat-datepicker-toggle>
                <mat-datepicker #endPicker></mat-datepicker>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>用章种类</mat-label>
                <mat-select formControlName="sealType">
                  <mat-option [value]="">全部</mat-option>
                  <mat-option value="公章">公章</mat-option>
                  <mat-option value="合同章">合同章</mat-option>
                  <mat-option value="财务章">财务章</mat-option>
                  <mat-option value="法人章">法人章</mat-option>
                  <mat-option value="其他">其他</mat-option>
                </mat-select>
              </mat-form-field>
            </div>

            <div class="filter-actions">
              <button mat-button (click)="resetFilters()">重置</button>
              <button mat-raised-button color="primary" (click)="applyFilters()">搜索</button>
              <button mat-raised-button color="accent" (click)="printByYear()">
                <mat-icon>print</mat-icon> 按年打印
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>

      <div class="seal-usage-table-container">
        <table mat-table [dataSource]="records" matSort (matSortChange)="sortData($event)" class="seal-usage-table">
          <!-- 序号 -->
          <ng-container matColumnDef="sequenceNumber">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> 序号 </th>
            <td mat-cell *matCellDef="let record"> 
              <span *ngIf="record.approver && record.sequence_number">（{{ record.year }}）公章使用{{ record.sequence_number }}号</span>
              <span *ngIf="!record.approver || !record.sequence_number" class="no-sequence">未审批</span>
            </td>
          </ng-container>

          <!-- 日期 -->
          <ng-container matColumnDef="date">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> 日期 </th>
            <td mat-cell *matCellDef="let record"> {{ record.date | date:'yyyy-MM-dd' }} </td>
          </ng-container>

          <!-- 事由 -->
          <ng-container matColumnDef="matter">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> 事由 </th>
            <td mat-cell *matCellDef="let record"> {{ record.matter }} </td>
          </ng-container>

          <!-- 文件名称 -->
          <ng-container matColumnDef="documentName">
            <th mat-header-cell *matHeaderCellDef> 文件名称 </th>
            <td mat-cell *matCellDef="let record"> {{ record.document_name }} </td>
          </ng-container>

          <!-- 数量 -->
          <ng-container matColumnDef="quantity">
            <th mat-header-cell *matHeaderCellDef> 数量 </th>
            <td mat-cell *matCellDef="let record"> {{ record.quantity }} </td>
          </ng-container>

          <!-- 用章种类 -->
          <ng-container matColumnDef="sealType">
            <th mat-header-cell *matHeaderCellDef> 用章种类 </th>
            <td mat-cell *matCellDef="let record"> {{ record.seal_type_display || record.seal_type }} </td>
          </ng-container>

          <!-- 受送单位 -->
          <ng-container matColumnDef="recipientUnit">
            <th mat-header-cell *matHeaderCellDef> 受送单位 </th>
            <td mat-cell *matCellDef="let record"> {{ record.recipient_unit }} </td>
          </ng-container>

          <!-- 使用人 -->
          <ng-container matColumnDef="user">
            <th mat-header-cell *matHeaderCellDef> 使用人 </th>
            <td mat-cell *matCellDef="let record"> {{ record.user_name }} </td>
          </ng-container>

          <!-- 审批人 -->
          <ng-container matColumnDef="approver">
            <th mat-header-cell *matHeaderCellDef> 审批人 </th>
            <td mat-cell *matCellDef="let record">
              <!-- 已审批，显示审批人 -->
              <span *ngIf="record.approver">{{ getUserDisplayName(record.approver) }}</span>
              
              <!-- 未审批且有审批权限，显示审批按钮 -->
              <button 
                *ngIf="!record.approver && authService.canAdminApproveCases()"
                mat-raised-button 
                color="primary" 
                size="small"
                class="approve-button"
                (click)="approveRecord(record); $event.stopPropagation()">
                <mat-icon class="small-icon">check_circle</mat-icon> 审批
              </button>
            </td>
          </ng-container>

          <!-- 盖章经办人 -->
          <ng-container matColumnDef="operator">
            <th mat-header-cell *matHeaderCellDef> 盖章经办人 </th>
            <td mat-cell *matCellDef="let record"> {{ record.operator_name }} </td>
          </ng-container>

          <!-- 备注 -->
          <ng-container matColumnDef="remarks">
            <th mat-header-cell *matHeaderCellDef> 备注 </th>
            <td mat-cell *matCellDef="let record"> {{ record.remarks }} </td>
          </ng-container>

          <!-- 操作 -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef> 操作 </th>
            <td mat-cell *matCellDef="let record">
              <!-- 编辑按钮 -->
              <button 
                mat-icon-button 
                color="primary" 
                matTooltip="编辑" 
                *ngIf="canManageRecord(record)"
                (click)="editRecord(record)">
                <mat-icon>edit</mat-icon>
              </button>

              <!-- 删除按钮 -->
              <button 
                mat-icon-button 
                color="warn" 
                matTooltip="删除" 
                *ngIf="canManageRecord(record)"
                (click)="deleteRecord(record)">
                <mat-icon>delete</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        <mat-paginator 
          [length]="totalItems" 
          [pageSize]="pageSize" 
          [pageSizeOptions]="[5, 10, 20, 50]"
          (page)="handlePageEvent($event)">
        </mat-paginator>
          </div>
        </div>
      </mat-tab>

      <!-- 函件登记表 Tab -->
      <mat-tab label="函件登记表">
        <div class="tab-content">
          <div class="tab-header">
            <h2>函件登记表</h2>
            <button mat-raised-button color="primary" (click)="openLetterDialog()">
              <mat-icon>add</mat-icon> 新增记录
            </button>
          </div>

          <mat-card class="filter-section">
            <mat-card-content>
              <form [formGroup]="letterFilterForm">
                <div class="filter-row">
                  <mat-form-field appearance="outline">
                    <mat-label>关键词</mat-label>
                    <input matInput formControlName="keyword" placeholder="搜索委托人、案件号等...">
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>年份</mat-label>
                    <mat-select formControlName="year">
                      <mat-option [value]="">全部</mat-option>
                      <mat-option *ngFor="let year of availableYears" [value]="year">{{ year }}</mat-option>
                    </mat-select>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>开始日期</mat-label>
                    <input matInput [matDatepicker]="letterStartPicker" formControlName="startDate">
                    <mat-datepicker-toggle matSuffix [for]="letterStartPicker"></mat-datepicker-toggle>
                    <mat-datepicker #letterStartPicker></mat-datepicker>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>结束日期</mat-label>
                    <input matInput [matDatepicker]="letterEndPicker" formControlName="endDate">
                    <mat-datepicker-toggle matSuffix [for]="letterEndPicker"></mat-datepicker-toggle>
                    <mat-datepicker #letterEndPicker></mat-datepicker>
                  </mat-form-field>

                  <mat-form-field appearance="outline">
                    <mat-label>函件类型</mat-label>
                    <mat-select formControlName="letterType">
                      <mat-option [value]="">全部</mat-option>
                      <mat-option *ngFor="let type of letterTypes" [value]="type">{{ type }}</mat-option>
                    </mat-select>
                  </mat-form-field>
                </div>

                <div class="filter-actions">
                  <button mat-button (click)="resetLetterFilters()">重置</button>
                  <button mat-raised-button color="primary" (click)="applyLetterFilters()">搜索</button>
                  <!-- <button mat-raised-button color="accent" (click)="printLetterByYear()">
                    <mat-icon>print</mat-icon> 按年打印
                  </button> -->
                </div>
              </form>
            </mat-card-content>
          </mat-card>

          <div class="letter-table-container">
            <table mat-table [dataSource]="letterRecords" matSort (matSortChange)="sortLetterData($event)" class="letter-table">
              <!-- 序号 -->
              <ng-container matColumnDef="sequenceNumber">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> 序号 </th>
                <td mat-cell *matCellDef="let record"> 
                  <span *ngIf="record.approver && record.sequence_number">{{ record.sequence_number }}</span>
                  <span *ngIf="!record.approver || !record.sequence_number" class="no-sequence">未审批</span>
                </td>
              </ng-container>

              <!-- 函数编号 -->
              <ng-container matColumnDef="letterNumber">
                <th mat-header-cell *matHeaderCellDef> 函数编号 </th>
                <td mat-cell *matCellDef="let record"> 
                  <span *ngIf="record.letter_number">（{{ record.year }}）{{ record.letter_type }}{{ record.letter_number }}号</span>
                  <span *ngIf="!record.letter_number">-</span>
                </td>
              </ng-container>

              <!-- 日期 -->
              <ng-container matColumnDef="date">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> 日期 </th>
                <td mat-cell *matCellDef="let record"> {{ record.date | date:'yyyy-MM-dd' }} </td>
              </ng-container>

              <!-- 对应案件号 -->
              <ng-container matColumnDef="caseNumber">
                <th mat-header-cell *matHeaderCellDef> 对应案件号 </th>
                <td mat-cell *matCellDef="let record"> {{ record.case_number || '' }} </td>
              </ng-container>

              <!-- 委托人/当事人 -->
              <ng-container matColumnDef="clientName">
                <th mat-header-cell *matHeaderCellDef> 委托人/当事人 </th>
                <td mat-cell *matCellDef="let record"> {{ record.client_name }} </td>
              </ng-container>

              <!-- 数量 -->
              <ng-container matColumnDef="quantity">
                <th mat-header-cell *matHeaderCellDef> 数量 </th>
                <td mat-cell *matCellDef="let record"> {{ record.quantity }} </td>
              </ng-container>

              <!-- 致函单位 -->
              <ng-container matColumnDef="recipientUnit">
                <th mat-header-cell *matHeaderCellDef> 致函单位 </th>
                <td mat-cell *matCellDef="let record"> {{ record.recipient_unit }} </td>
              </ng-container>

              <!-- 使用律师 -->
              <ng-container matColumnDef="lawyerName">
                <th mat-header-cell *matHeaderCellDef> 使用律师 </th>
                <td mat-cell *matCellDef="let record"> {{ record.lawyer_name }} </td>
              </ng-container>

              <!-- 函件类型 -->
              <ng-container matColumnDef="letterType">
                <th mat-header-cell *matHeaderCellDef> 函件类型 </th>
                <td mat-cell *matCellDef="let record"> {{ record.letter_type_display || record.letter_type }} </td>
              </ng-container>

              <!-- 审批人 -->
              <ng-container matColumnDef="approver">
                <th mat-header-cell *matHeaderCellDef> 审批人 </th>
                <td mat-cell *matCellDef="let record">
                  <!-- 已审批，显示审批人 -->
                  <span *ngIf="record.approver">{{ getUserDisplayName(record.approver) }}</span>
                  
                  <!-- 未审批且有审批权限，显示审批按钮 -->
                  <button 
                    *ngIf="!record.approver && authService.canAdminApproveCases()"
                    mat-raised-button 
                    color="primary" 
                    size="small"
                    class="approve-button"
                    (click)="approveLetterRecord(record); $event.stopPropagation()">
                    <mat-icon class="small-icon">check_circle</mat-icon> 审批
                  </button>
                </td>
              </ng-container>

              <!-- 备注 -->
              <ng-container matColumnDef="remarks">
                <th mat-header-cell *matHeaderCellDef> 备注 </th>
                <td mat-cell *matCellDef="let record"> {{ record.remarks }} </td>
              </ng-container>

              <!-- 操作 -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef> 操作 </th>
                <td mat-cell *matCellDef="let record">
                  <!-- 打印按钮 -->
                  <button 
                    mat-icon-button 
                    color="accent" 
                    matTooltip="打印（包含该函件在内向前推12个相同类型函件）" 
                    *ngIf="record.approver && record.sequence_number"
                    (click)="printSingleLetterRecord(record)">
                    <mat-icon>print</mat-icon>
                  </button>

                  <!-- 编辑按钮 -->
                  <button 
                    mat-icon-button 
                    color="primary" 
                    matTooltip="编辑" 
                    *ngIf="canManageLetterRecord(record)"
                    (click)="editLetterRecord(record)">
                    <mat-icon>edit</mat-icon>
                  </button>

                  <!-- 删除按钮 -->
                  <button 
                    mat-icon-button 
                    color="warn" 
                    matTooltip="删除" 
                    *ngIf="canManageLetterRecord(record)"
                    (click)="deleteLetterRecord(record)">
                    <mat-icon>delete</mat-icon>
                  </button>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="letterDisplayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: letterDisplayedColumns;"></tr>
            </table>

            <mat-paginator 
              [length]="letterTotalItems" 
              [pageSize]="letterPageSize" 
              [pageSizeOptions]="[5, 10, 20, 50]"
              (page)="handleLetterPageEvent($event)">
            </mat-paginator>
          </div>
        </div>
      </mat-tab>
    </mat-tab-group>
    </div>
  `,
  styles: [`
    .container {
      padding: 20px;
    }
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }
    .tab-content {
      padding: 20px 0;
    }
    .tab-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }
    .filter-section {
      margin-bottom: 20px;
    }
    .filter-row {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
    }
    .filter-actions {
      display: flex;
      justify-content: flex-end;
      gap: 8px;
      margin-top: 8px;
    }
    .seal-usage-table-container, .letter-table-container {
      overflow-x: auto;
    }
    .seal-usage-table, .letter-table {
      width: 100%;
    }
    mat-form-field {
      flex: 1;
      min-width: 200px;
    }
    .approve-button {
      line-height: 28px;
      font-size: 12px;
      padding: 0 8px;
    }
    .small-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
      line-height: 16px;
      vertical-align: middle;
      margin-right: 4px;
    }
    .no-sequence {
      color: #999;
      font-style: italic;
    }
    @media (max-width: 768px) {
      .filter-row {
        flex-direction: column;
      }
      mat-form-field {
        width: 100%;
      }
    }
  `]
})
export class SealUsageListComponent implements OnInit {
  // Tab 相关
  selectedTabIndex: number = 0;

  // 公章使用登记表相关
  records: SealUsageRecord[] = [];
  allRecords: SealUsageRecord[] = []; // 存储所有记录
  displayedColumns: string[] = [
    'sequenceNumber', 'date', 'matter', 'documentName', 'quantity', 
    'sealType', 'recipientUnit', 'user', 'approver', 'operator', 'remarks', 'actions'
  ];
  filterForm = new FormGroup({
    keyword: new FormControl(''),
    year: new FormControl<number | null>(null),
    startDate: new FormControl<Date | null>(null),
    endDate: new FormControl<Date | null>(null),
    sealType: new FormControl('')
  });
  totalItems: number = 0;
  pageSize: number = 10;
  pageIndex: number = 0;

  // 函件登记表相关
  letterRecords: LetterRecord[] = [];
  allLetterRecords: LetterRecord[] = []; // 存储所有函件记录
  letterDisplayedColumns: string[] = [
    'sequenceNumber', 'letterNumber', 'date', 'caseNumber', 'clientName', 'quantity', 
    'recipientUnit', 'lawyerName', 'letterType', 'approver', 'remarks', 'actions'
  ];
  letterFilterForm = new FormGroup({
    keyword: new FormControl(''),
    year: new FormControl<number | null>(null),
    startDate: new FormControl<Date | null>(null),
    endDate: new FormControl<Date | null>(null),
    letterType: new FormControl('')
  });
  letterTotalItems: number = 0;
  letterPageSize: number = 10;
  letterPageIndex: number = 0;

  // 共用属性
  currentUser: User | null = null;
  availableYears: number[] = [];
  getUserDisplayName = getUserDisplayName;
  letterTypes: string[] = [];

  constructor(
    private caseService: CaseService,
    public authService: AuthService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private datePipe: DatePipe,
    private firmInfoService: FirmInfoService
  ) {}

  ngOnInit(): void {
    this.loadRecords();
    this.loadLetterRecords();
    this.generateAvailableYears();
    this.authService.getCurrentUser().subscribe(user => {
      this.currentUser = user;
    });
    this.initializeLetterTypes();
  }

  generateAvailableYears(): void {
    const currentYear = new Date().getFullYear();
    this.availableYears = [];
    for (let year = currentYear; year >= currentYear - 10; year--) {
      this.availableYears.push(year);
    }
  }

  loadRecords(): void {
    const filters: any = {};
    
    const keyword = this.filterForm.get('keyword')?.value;
    if (keyword) {
      filters.keyword = keyword;
    }
    
    const year = this.filterForm.get('year')?.value;
    if (year) {
      filters.year = year;
    }
    
    const startDate = this.filterForm.get('startDate')?.value;
    if (startDate) {
      filters.start_date = this.datePipe.transform(startDate, 'yyyy-MM-dd');
    }
    
    const endDate = this.filterForm.get('endDate')?.value;
    if (endDate) {
      filters.end_date = this.datePipe.transform(endDate, 'yyyy-MM-dd');
    }
    
    const sealType = this.filterForm.get('sealType')?.value;
    if (sealType) {
      filters.seal_type = sealType;
    }
    
    this.caseService.getSealUsageRecords(filters).subscribe({
      next: (data) => {
        this.allRecords = data;
        this.totalItems = data.length;
        this.updateDisplayedRecords();
      },
      error: (error) => {
        console.error('获取公章使用记录失败', error);
        this.snackBar.open('获取公章使用记录失败', '关闭', {
          duration: 3000
        });
      }
    });
  }

  updateDisplayedRecords(): void {
    const startIndex = this.pageIndex * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.records = this.allRecords.slice(startIndex, endIndex);
  }

  applyFilters(): void {
    this.pageIndex = 0; // 重置到第一页
    this.loadRecords();
  }

  resetFilters(): void {
    this.filterForm.reset();
    this.pageIndex = 0; // 重置到第一页
    this.loadRecords();
  }

  sortData(sort: Sort): void {
    // 实现排序逻辑
    if (!sort.active || sort.direction === '') {
      return;
    }
    
    this.records = this.records.sort((a, b) => {
      const isAsc = sort.direction === 'asc';
      switch (sort.active) {
        case 'date':
          return this.compare(a.date, b.date, isAsc);
        case 'sequenceNumber':
          return this.compare(a.sequence_number, b.sequence_number, isAsc);
        case 'matter':
          return this.compare(a.matter, b.matter, isAsc);
        default:
          return 0;
      }
    });
  }

  compare(a: string | number, b: string | number, isAsc: boolean): number {
    return (a < b ? -1 : 1) * (isAsc ? 1 : -1);
  }

  handlePageEvent(event: PageEvent): void {
    this.pageSize = event.pageSize;
    this.pageIndex = event.pageIndex;
    this.updateDisplayedRecords();
  }

  openSealUsageDialog(): void {
    const dialogRef = this.dialog.open(SealUsageDialogComponent, {
      width: '600px',
      data: null
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadRecords();
      }
    });
  }

  editRecord(record: SealUsageRecord): void {
    const dialogRef = this.dialog.open(SealUsageDialogComponent, {
      width: '600px',
      data: record
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadRecords();
      }
    });
  }

  approveRecord(record: SealUsageRecord): void {
    if (!record.id) return;
    
    this.caseService.approveSealUsageRecord(record.id).subscribe({
      next: () => {
        this.snackBar.open('记录审批成功', '关闭', {
          duration: 3000
        });
        this.loadRecords();
      },
      error: (error) => {
        console.error('记录审批失败', error);
        this.snackBar.open('记录审批失败: ' + (error.error?.detail || '未知错误'), '关闭', {
          duration: 3000
        });
      }
    });
  }

  printByYear(): void {
    const year = this.filterForm.get('year')?.value;
    if (!year) {
      this.snackBar.open('请先选择年份', '关闭', {
        duration: 3000
      });
      return;
    }
    
    this.caseService.printSealUsageRecordsByYear(year).subscribe({
      next: (data) => {
        this.caseService.openSealUsagePrintWindow(data);
      },
      error: (error) => {
        console.error('准备打印数据失败', error);
        this.snackBar.open('准备打印数据失败', '关闭', {
          duration: 3000
        });
      }
    });
  }

  deleteRecord(record: SealUsageRecord): void {
    if (!record.id) return;
    
    if (confirm('确定要删除这条记录吗？')) {
      this.caseService.deleteSealUsageRecord(record.id).subscribe({
        next: () => {
          this.snackBar.open('记录删除成功', '关闭', {
            duration: 3000
          });
          this.loadRecords();
        },
        error: (error) => {
          console.error('记录删除失败', error);
          this.snackBar.open('记录删除失败', '关闭', {
            duration: 3000
          });
        }
      });
    }
  }

  canManageRecord(record: SealUsageRecord): boolean {
    if (!this.currentUser || !record.user_name) return false;
    
    // 使用人可以编辑自己的记录（如果还没有审批）
    const currentUserDisplayName = this.getUserDisplayName(this.currentUser);
    return currentUserDisplayName === record.user_name && !record.approver;
  }

  // Tab 相关方法
  onTabChange(event: any): void {
    this.selectedTabIndex = event.index;
    if (event.index === 0) {
      this.loadRecords();
    } else if (event.index === 1) {
      this.loadLetterRecords();
    }
  }

  // 函件登记表相关方法
  loadLetterRecords(): void {
    const filters: any = {};
    
    const keyword = this.letterFilterForm.get('keyword')?.value;
    if (keyword) {
      filters.keyword = keyword;
    }
    
    const year = this.letterFilterForm.get('year')?.value;
    if (year) {
      filters.year = year;
    }
    
    const startDate = this.letterFilterForm.get('startDate')?.value;
    if (startDate) {
      filters.start_date = this.datePipe.transform(startDate, 'yyyy-MM-dd');
    }
    
    const endDate = this.letterFilterForm.get('endDate')?.value;
    if (endDate) {
      filters.end_date = this.datePipe.transform(endDate, 'yyyy-MM-dd');
    }
    
    const letterType = this.letterFilterForm.get('letterType')?.value;
    if (letterType) {
      filters.letter_type = letterType;
    }
    
    this.caseService.getLetterRecords(filters).subscribe({
      next: (data) => {
        this.allLetterRecords = data;
        this.letterTotalItems = data.length;
        this.updateDisplayedLetterRecords();
      },
      error: (error) => {
        console.error('获取函件记录失败', error);
        this.snackBar.open('获取函件记录失败', '关闭', {
          duration: 3000
        });
      }
    });
  }

  updateDisplayedLetterRecords(): void {
    const startIndex = this.letterPageIndex * this.letterPageSize;
    const endIndex = startIndex + this.letterPageSize;
    this.letterRecords = this.allLetterRecords.slice(startIndex, endIndex);
  }

  applyLetterFilters(): void {
    this.letterPageIndex = 0; // 重置到第一页
    this.loadLetterRecords();
  }

  resetLetterFilters(): void {
    this.letterFilterForm.reset();
    this.letterPageIndex = 0; // 重置到第一页
    this.loadLetterRecords();
  }

  sortLetterData(sort: Sort): void {
    if (!sort.active || sort.direction === '') {
      return;
    }
    
    this.letterRecords = this.letterRecords.sort((a, b) => {
      const isAsc = sort.direction === 'asc';
      switch (sort.active) {
        case 'date':
          return this.compare(a.date, b.date, isAsc);
        case 'sequenceNumber':
          return this.compare(a.sequence_number, b.sequence_number, isAsc);
        case 'clientName':
          return this.compare(a.client_name, b.client_name, isAsc);
        default:
          return 0;
      }
    });
  }

  handleLetterPageEvent(event: PageEvent): void {
    this.letterPageSize = event.pageSize;
    this.letterPageIndex = event.pageIndex;
    this.updateDisplayedLetterRecords();
  }

  openLetterDialog(): void {
    const dialogRef = this.dialog.open(LetterDialogComponent, {
      width: '600px',
      data: null
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadLetterRecords();
      }
    });
  }

  editLetterRecord(record: LetterRecord): void {
    const dialogRef = this.dialog.open(LetterDialogComponent, {
      width: '600px',
      data: record
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadLetterRecords();
      }
    });
  }

  approveLetterRecord(record: LetterRecord): void {
    if (!record.id) return;
    
    this.caseService.approveLetterRecord(record.id).subscribe({
      next: () => {
        this.snackBar.open('函件记录审批成功', '关闭', {
          duration: 3000
        });
        this.loadLetterRecords();
      },
      error: (error) => {
        console.error('函件记录审批失败', error);
        this.snackBar.open('函件记录审批失败: ' + (error.error?.detail || '未知错误'), '关闭', {
          duration: 3000
        });
      }
    });
  }

  printLetterByYear(): void {
    const year = this.letterFilterForm.get('year')?.value;
    const letterType = this.letterFilterForm.get('letterType')?.value;
    
    if (!year) {
      this.snackBar.open('请先选择年份', '关闭', {
        duration: 3000
      });
      return;
    }
    
    if (!letterType) {
      this.snackBar.open('请先选择函件类型', '关闭', {
        duration: 3000
      });
      return;
    }
    
    this.caseService.printLetterRecordsByYear(year, letterType).subscribe({
      next: (data) => {
        this.caseService.openLetterPrintWindow(data);
      },
      error: (error) => {
        console.error('准备打印数据失败', error);
        this.snackBar.open('准备打印数据失败', '关闭', {
          duration: 3000
        });
      }
    });
  }

  deleteLetterRecord(record: LetterRecord): void {
    if (!record.id) return;
    
    if (confirm('确定要删除这条函件记录吗？')) {
      this.caseService.deleteLetterRecord(record.id).subscribe({
        next: () => {
          this.snackBar.open('函件记录删除成功', '关闭', {
            duration: 3000
          });
          this.loadLetterRecords();
        },
        error: (error) => {
          console.error('函件记录删除失败', error);
          this.snackBar.open('函件记录删除失败', '关闭', {
            duration: 3000
          });
        }
      });
    }
  }

  canManageLetterRecord(record: LetterRecord): boolean {
    if (!this.currentUser || !record.lawyer_name) return false;
    
    // 使用律师可以编辑自己的记录（如果还没有审批）
    const currentUserDisplayName = this.getUserDisplayName(this.currentUser);
    return currentUserDisplayName === record.lawyer_name && !record.approver;
  }

  /**
   * 根据律师事务所初始化函件类型
   */
  initializeLetterTypes(): void {
    const firmName = this.firmInfoService.firmName;
    
    if (firmName === '广东承诺律师事务所雷州分所') {
      // 雷州分所的函件类型
      this.letterTypes = [
        '广承雷民函',
        '广承雷刑函',
        '广承雷行函',
        '广承雷刑会',
        '广承雷律调',
        '广承雷律师函',
        '广承雷意见'
      ];
    } else {
      // 总所的函件类型
      this.letterTypes = [
        '广承民函',
        '广承刑函',
        '广承行函',
        '广承刑会',
        '广承律调',
        '广承律师函',
        '广承意见'
      ];
    }
  }

  printSingleLetterRecord(record: LetterRecord): void {
    if (!record.id) return;
    
    // 直接打开打印页面，从当前函件开始向前推12条相同类型的函件
    this.caseService.printLetterRecord(record.id);
  }
} 