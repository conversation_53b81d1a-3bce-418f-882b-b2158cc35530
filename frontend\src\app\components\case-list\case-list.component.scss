.case-list-container {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 0 1rem;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h2 {
      margin: 0;
    }
  }

  // 过滤器容器
  .filters-section {
    margin-bottom: 20px;
  }

  // 过滤器卡片
  .filter-card {
    background: #f8f9fa;
    border-radius: 4px;
    padding: 16px 20px;
    border: 1px solid #e9ecef;
    margin-bottom: 16px;
  }

  // 过滤器行
  .filter-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: center;
  }

  // 过滤器组（用于toggle group等非表单字段）
  .filter-group {
    display: flex;
    align-items: center;
    
    mat-button-toggle-group {
      border-radius: 4px;
      overflow: hidden;
      
      mat-button-toggle {
        font-size: 14px;
        padding: 0 16px;
        height: 40px;
        border: none !important;
        
        &.mat-button-toggle-checked {
          background-color: #2196f3;
          color: white;
        }
        
        &:not(.mat-button-toggle-checked) {
          background-color: white;
          color: #666;
          border-right: 1px solid #e0e0e0 !important;
        }
        
        &:hover:not(.mat-button-toggle-checked) {
          background-color: #f5f5f5;
        }
      }
    }
  }

  // 过滤器字段
  .filter-field {
    flex: 0 0 auto;
    min-width: 200px;
    max-width: 250px;
    
    .mat-mdc-form-field {
      font-size: 14px;
    }
    
    .mat-mdc-select-trigger {
      height: 40px;
    }
  }

  // 过滤器操作按钮
  .filter-actions {
    display: flex;
    gap: 8px;
    align-items: center;
    margin-left: auto;
    
    button[mat-icon-button] {
      color: #666;
      width: 36px;
      height: 36px;
      
      &:hover {
        color: #f44336;
        background-color: rgba(244, 67, 54, 0.1);
      }
      
      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .filter-row {
      gap: 16px;
    }
    
    .filter-field {
      min-width: 180px;
      max-width: 220px;
    }
  }

  @media (max-width: 1024px) {
    .filter-row {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;
    }

    .filter-group {
      justify-content: center;
      
      mat-button-toggle-group {
        width: fit-content;
      }
    }

    .filter-field {
      width: 100%;
      max-width: none;
    }

    .filter-actions {
      justify-content: center;
      margin-left: 0;
    }
  }

  @media (max-width: 768px) {
    .header {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;
    }

    .filter-card {
      padding: 12px 16px;
      margin-bottom: 12px;
    }
    
    .filter-group {
      mat-button-toggle-group {
        width: 100%;
        
        mat-button-toggle {
          flex: 1;
          font-size: 13px;
        }
      }
    }
  }

  .loading-spinner {
    display: flex;
    justify-content: center;
    padding: 20px;
  }

  .error-message {
    color: red;
    text-align: center;
    padding: 20px;
  }

  table {
    width: 100%;

    .mat-row:hover {
      background-color: #f5f5f5;
    }

    .mat-column-actions {
      width: 120px;
      min-width: 120px;
      text-align: center;
      white-space: nowrap;
      padding: 0 8px;
      
      button {
        margin: 0 2px;
        min-width: auto;
        
        &[mat-icon-button] {
          width: 36px;
          height: 36px;
          line-height: 36px;
          
          mat-icon {
            font-size: 20px;
            width: 20px;
            height: 20px;
          }
        }
      }
    }
  }

  // 过滤结果信息
  .filter-results-info {
    margin-bottom: 16px;
    padding: 8px 16px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 4px solid #2196f3;

    .results-count {
      color: #333;
      font-size: 0.9rem;
      font-weight: 500;

      .filter-info {
        color: #666;
        font-weight: normal;
        font-style: italic;
      }
    }
  }
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  
  h1 {
    margin: 0;
    color: #3f51b5;
  }
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  display: inline-block;
  margin-right: 8px;

  &.created {
    background-color: #e3f2fd;
    color: #1976d2;
  }
  &.applying {
    background-color: #fff3e0;
    color: #f57c00;
  }
  &.admin_reviewing {
    background-color: #e8f5e9;
    color: #388e3c;
  }
  &.director_reviewing {
    background-color: #f3e5f5;
    color: #8e24aa;
  }
  &.processing_delegation {
    background-color: #e0f7fa;
    color: #00acc1;
  }
  &.in_progress {
    background-color: #e8eaf6;
    color: #3f51b5;
  }
  &.applying_closure {
    background-color: #fce4ec;
    color: #d81b60;
  }
  &.closure_approved {
    background-color: #f1f8e9;
    color: #689f38;
  }
  &.archived {
    background-color: #eceff1;
    color: #546e7a;
  }
}

.paid-icon {
  color: #4caf50;
  font-size: 16px;
  vertical-align: middle;
  margin-left: 4px;
}

.mat-column-status {
  min-width: 160px;
}

// 当事人列表样式
.parties-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
  max-width: 200px;
}

.party-item {
  font-size: 0.9em;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.more-parties {
  color: #666;
  font-size: 0.85em;
  font-style: italic;
}

.no-parties {
  color: #999;
  font-style: italic;
  font-size: 0.9em;
}

// 移动设备响应式设计
@media (max-width: 768px) {
  .case-list-container {
    table {
      .mat-column-actions {
        width: 100px;
        min-width: 100px;
        padding: 0 4px;
        
        button[mat-icon-button] {
          width: 32px;
          height: 32px;
          line-height: 32px;
          margin: 0 1px;
          
          mat-icon {
            font-size: 18px;
            width: 18px;
            height: 18px;
          }
        }
      }
    }
  }
}
