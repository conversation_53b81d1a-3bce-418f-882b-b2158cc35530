from rest_framework import serializers
from django.contrib.auth.models import User
from .models import Case, NaturalPerson, LegalEntity, CaseApproval, CaseParty, CaseLawyerCollaboration, LegacyCase, SealUsageRecord, LetterRecord

class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name']

class NaturalPersonSerializer(serializers.ModelSerializer):
    creator = UserSerializer(read_only=True)
    
    class Meta:
        model = NaturalPerson
        fields = [
            'id', 'name', 'id_number', 'phone_number', 'remarks',
            'creator', 'created_at', 'updated_at'
        ]
        read_only_fields = ['creator', 'created_at', 'updated_at']

    def validate(self, data):
        # 验证姓名不能为空
        if not data.get('name'):
            raise serializers.ValidationError('必须提供姓名')
        
        # 如果同时提供了身份证号码和手机号码，都不能为空
        if data.get('id_number') == '' and data.get('phone_number') == '':
            # 如果两个字段都是空字符串，清理为 None
            data['id_number'] = None
            data['phone_number'] = None
        
        return data

class LegalEntitySerializer(serializers.ModelSerializer):
    creator = UserSerializer(read_only=True)
    
    class Meta:
        model = LegalEntity
        fields = [
            'id', 'name', 'credit_code', 'representative_name',
            'representative_id_number', 'representative_phone_number', 'remarks',
            'creator', 'created_at', 'updated_at'
        ]
        read_only_fields = ['creator', 'created_at', 'updated_at']

    def validate(self, data):
        if not data.get('name'):
            raise serializers.ValidationError('必须提供单位名称')
        return data

class CasePartySerializer(serializers.ModelSerializer):
    natural_person = NaturalPersonSerializer(read_only=True)
    legal_entity = LegalEntitySerializer(read_only=True)
    party_type_display = serializers.CharField(source='get_party_type_display', read_only=True)
    
    class Meta:
        model = CaseParty
        fields = [
            'id', 'case', 'natural_person', 'legal_entity',
            'party_type', 'party_type_display', 'is_client', 'internal_number', 
            'remarks', 'created_at'
        ]
        read_only_fields = ['created_at']

class CasePartyCreateSerializer(serializers.ModelSerializer):
    """用于创建案件当事人关系的序列化器"""
    class Meta:
        model = CaseParty
        fields = ['case', 'natural_person', 'legal_entity', 'party_type', 'is_client', 'internal_number', 'remarks']

    def validate(self, data):
        # 获取当前实例（用于区分创建和更新）
        instance = getattr(self, 'instance', None)
        
        # 验证只能关联一种当事人类型
        if bool(data.get('natural_person')) == bool(data.get('legal_entity')):
            raise serializers.ValidationError('必须且只能关联一个自然人或单位')

        # 验证同一个当事人在一个案件中只能出现一次
        if natural_person := data.get('natural_person'):
            query = CaseParty.objects.filter(
                case=data['case'],
                natural_person=natural_person
            )
            
            # 如果是更新操作，排除当前实例
            if instance:
                query = query.exclude(pk=instance.pk)
                
            if query.exists():
                raise serializers.ValidationError('同一个自然人在一个案件中只能出现一次')

        if legal_entity := data.get('legal_entity'):
            query = CaseParty.objects.filter(
                case=data['case'],
                legal_entity=legal_entity
            )
            
            # 如果是更新操作，排除当前实例
            if instance:
                query = query.exclude(pk=instance.pk)
                
            if query.exists():
                raise serializers.ValidationError('同一个单位在一个案件中只能出现一次')

        return data

class CaseApprovalSerializer(serializers.ModelSerializer):
    approver = UserSerializer(read_only=True)
    action_display = serializers.CharField(source='get_action_display', read_only=True)

    class Meta:
        model = CaseApproval
        fields = ['id', 'case', 'approver', 'action', 'action_display', 'comment', 'created_at', 'status_when_approved']
        read_only_fields = ['approver']

class CaseLawyerCollaborationSerializer(serializers.ModelSerializer):
    lawyer = UserSerializer(read_only=True)
    
    class Meta:
        model = CaseLawyerCollaboration
        fields = ['id', 'case', 'lawyer', 'fee_share_ratio', 'joined_at', 'remarks']
        read_only_fields = ['joined_at']

class CaseLawyerCollaborationCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = CaseLawyerCollaboration
        fields = ['case', 'lawyer', 'fee_share_ratio', 'remarks']
        
    def validate(self, data):
        # 获取当前实例（用于区分创建和更新）
        instance = getattr(self, 'instance', None)
        
        # 如果是更新操作且没有修改case和lawyer字段，则只验证remarks
        if instance and not (data.get('case') or data.get('lawyer')):
            return data
            
        # 验证不能将案件创建人添加为协作律师
        if 'lawyer' in data and 'case' in data and data['lawyer'] == data['case'].lawyer:
            raise serializers.ValidationError('不能将案件创建人添加为协作律师')
            
        # 验证律师不能重复添加
        case = data.get('case') or (instance.case if instance else None)
        lawyer = data.get('lawyer') or (instance.lawyer if instance else None)
        
        if case and lawyer:
            existing_query = CaseLawyerCollaboration.objects.filter(
                case=case,
                lawyer=lawyer
            )
            
            # 如果是更新操作，排除当前实例
            if instance:
                existing_query = existing_query.exclude(pk=instance.pk)
                
            if existing_query.exists():
                raise serializers.ValidationError('该律师已经是此案件的协作律师')
            
        return data

class CaseSearchSerializer(serializers.ModelSerializer):
    """用于案件搜索结果的序列化器，包含委托人信息"""
    lawyer = UserSerializer(read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    clients = serializers.SerializerMethodField()
    
    class Meta:
        model = Case
        fields = [
            'id', 'case_number', 'case_cause', 'lawyer',
            'status', 'status_display', 'is_paid', 'clients'
        ]
        
    def get_clients(self, obj):
        """获取委托人信息"""
        client_parties = obj.party_relationships.filter(is_client=True)
        clients = []
        
        for party in client_parties:
            client_info = {}
            if party.natural_person:
                client_info = {
                    'id': party.natural_person.id,
                    'name': party.natural_person.name,
                    'type': '自然人',
                    'id_number': party.natural_person.id_number or ''
                }
            elif party.legal_entity:
                client_info = {
                    'id': party.legal_entity.id,
                    'name': party.legal_entity.name,
                    'type': '法人',
                    'id_number': party.legal_entity.representative_id_number or ''
                }
            
            if client_info:
                clients.append(client_info)
        
        return clients

class CaseSerializer(serializers.ModelSerializer):
    lawyer = UserSerializer(read_only=True)
    collaborating_lawyers = serializers.SerializerMethodField()
    parties = CasePartySerializer(source='party_relationships', many=True, read_only=True)
    approvals = CaseApprovalSerializer(many=True, read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = Case
        fields = [
            'id', 'case_number', 'case_cause', 'lawyer', 'collaborating_lawyers',
            'status', 'status_display', 'is_paid', 'is_sensitive', 'is_risk_agency', 'agreed_lawyer_fee',
            'created_at', 'updated_at', 'content', 'parties', 'approvals'
        ]
        read_only_fields = ('lawyer',)
        
    def get_collaborating_lawyers(self, obj):
        """获取协作律师列表"""
        collaborations = obj.lawyer_collaborations.all()
        return UserSerializer([collab.lawyer for collab in collaborations], many=True).data 

class LegacyCaseSerializer(serializers.ModelSerializer):
    lawyer = UserSerializer(read_only=True)
    approver = UserSerializer(read_only=True)
    case_type_display = serializers.CharField(source='get_case_type_display', read_only=True)
    
    class Meta:
        model = LegacyCase
        fields = [
            'id', 'contract_number', 'filing_date', 'client', 'opposing_party',
            'criminal', 'case_cause', 'court', 'trial_level', 'lawyer',
            'case_type', 'case_type_display', 'fee_amount', 'is_fee_compliant',
            'invoice_number', 'is_conflict', 'is_sensitive', 'approver',
            'closing_date', 'archive_date', 'remarks', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class SealUsageRecordSerializer(serializers.ModelSerializer):
    """公章使用登记表序列化器"""
    approver = UserSerializer(read_only=True)
    seal_type_display = serializers.CharField(source='get_seal_type_display', read_only=True)
    
    class Meta:
        model = SealUsageRecord
        fields = [
            'id', 'year', 'sequence_number', 'date', 'matter',
            'document_name', 'quantity', 'seal_type', 'seal_type_display',
            'recipient_unit', 'user_name', 'approver', 'operator_name', 'remarks',
            'created_at', 'updated_at', 'last_printed_at'
        ]
        read_only_fields = ['year', 'sequence_number', 'created_at', 'updated_at', 'last_printed_at']

class SealUsageRecordCreateSerializer(serializers.ModelSerializer):
    """创建公章使用登记表的序列化器"""
    operator_name = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    
    class Meta:
        model = SealUsageRecord
        fields = [
            'date', 'matter', 'document_name', 'quantity', 'seal_type',
            'recipient_unit', 'user_name', 'operator_name', 'remarks'
        ]
    
    def create(self, validated_data):
        # 创建记录时自动设置年份和序号
        return SealUsageRecord.objects.create(**validated_data)

class LetterRecordSerializer(serializers.ModelSerializer):
    """函件登记表序列化器"""
    approver = UserSerializer(read_only=True)
    letter_type_display = serializers.CharField(source='get_letter_type_display', read_only=True)

    class Meta:
        model = LetterRecord
        fields = [
            'id', 'year', 'letter_number', 'sequence_number', 'date', 'case_number', 'client_name',
            'quantity', 'recipient_unit', 'lawyer_name', 'letter_type', 'letter_type_display',
            'approver', 'remarks', 'created_at', 'updated_at', 'last_printed_at'
        ]
        read_only_fields = ['year', 'letter_number', 'sequence_number', 'created_at', 'updated_at', 'last_printed_at']

class LetterRecordCreateSerializer(serializers.ModelSerializer):
    """创建函件登记表的序列化器"""
    class Meta:
        model = LetterRecord
        fields = [
            'date', 'case_number', 'client_name', 'quantity', 'recipient_unit',
            'lawyer_name', 'letter_type', 'remarks'
        ]
    
    def create(self, validated_data):
        # 创建记录时自动设置年份和序号
        return LetterRecord.objects.create(**validated_data)

# 通用搜索序列化器
class UniversalSearchResultSerializer(serializers.Serializer):
    """通用搜索结果序列化器"""
    id = serializers.IntegerField()
    name = serializers.CharField()
    type = serializers.CharField()  # 'natural_person', 'legal_entity', 'legacy_case'
    type_display = serializers.CharField()  # '自然人', '企业法人', '历史案件'
    additional_info = serializers.DictField()  # 额外信息，根据类型不同而不同
    
    def get_db_display_name(self, db_alias):
        """获取数据库的友好显示名称"""
        db_name_mapping = {
            'default': '总所数据库',
            'firm2': '分所数据库',
        }
        return db_name_mapping.get(db_alias, f'数据库({db_alias})')
    
    def to_representation(self, instance):
        """根据不同的实例类型返回不同的数据结构"""
        # 获取数据库来源信息（如果有的话）
        db_source = getattr(instance, '_db_source', 'default')
        db_display_name = self.get_db_display_name(db_source)
        
        # 安全地获取用户显示名称，避免跨数据库查询问题
        def safe_get_creator_name(creator):
            if not creator:
                return '未知用户'
            try:
                # 直接使用已加载的用户属性，避免额外的数据库查询
                if creator.last_name or creator.first_name:
                    display_name = f"{creator.last_name or ''}{creator.first_name or ''}".strip()
                    if display_name:
                        return display_name
                return creator.username or f'用户ID: {creator.id}'
            except Exception as e:
                return f'用户ID: {creator.id if creator else "未知"}'
        
        if isinstance(instance, NaturalPerson):
            return {
                'id': instance.id,
                'name': instance.name,
                'type': 'natural_person',
                'type_display': '自然人',
                'additional_info': {
                    'id_number': instance.id_number,
                    'phone_number': instance.phone_number,
                    'creator': safe_get_creator_name(instance.creator),
                    'created_at': instance.created_at.isoformat() if instance.created_at else None,
                    'db_source': db_source,
                    'db_display_name': db_display_name,
                }
            }
        elif isinstance(instance, LegalEntity):
            return {
                'id': instance.id,
                'name': instance.name,
                'type': 'legal_entity',
                'type_display': '企业法人',
                'additional_info': {
                    'credit_code': instance.credit_code,
                    'representative_name': instance.representative_name,
                    'representative_id_number': instance.representative_id_number,
                    'creator': safe_get_creator_name(instance.creator),
                    'created_at': instance.created_at.isoformat() if instance.created_at else None,
                    'db_source': db_source,
                    'db_display_name': db_display_name,
                }
            }
        elif isinstance(instance, LegacyCase):
            return {
                'id': instance.id,
                'name': f"{instance.case_type or '未知类型'} - {instance.parties or '未知当事人'}",
                'type': 'legacy_case',
                'type_display': '遗留案件',
                'additional_info': {
                    'case_number': instance.case_number,
                    'case_type': instance.case_type,
                    'parties': instance.parties,
                    'case_amount': str(instance.case_amount) if instance.case_amount else None,
                    'case_stage': instance.case_stage,
                    'creator': safe_get_creator_name(instance.creator),
                    'created_at': instance.created_at.isoformat() if instance.created_at else None,
                    'db_source': db_source,
                    'db_display_name': db_display_name,
                }
            }
        else:
            return super().to_representation(instance) 